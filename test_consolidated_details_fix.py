#!/usr/bin/env python3
"""
Test script to verify the consolidated_details JSON parsing fix
"""

import sys
import json
import sqlite3
from pathlib import Path

def test_consolidated_details_parsing():
    """Test if consolidated_details are properly parsed from JSON strings to arrays"""
    print("🔧 TESTING CONSOLIDATED DETAILS JSON PARSING FIX")
    print("=" * 60)
    
    try:
        # Import the phased process manager
        sys.path.append('core')
        from phased_process_manager import PhasedProcessManager
        
        print("✅ Successfully imported PhasedProcessManager")
        
        # Initialize manager
        manager = PhasedProcessManager(debug=True)
        print("✅ Successfully initialized PhasedProcessManager")
        
        # Test the _parse_consolidated_details method directly
        print("\n📋 TESTING _parse_consolidated_details METHOD:")
        
        # Test 1: Valid JSON array
        test_json = '[{"id": "test1", "name": "Test Item 1"}, {"id": "test2", "name": "Test Item 2"}]'
        result = manager._parse_consolidated_details(test_json)
        
        if isinstance(result, list) and len(result) == 2:
            print("✅ Test 1 PASSED: Valid JSON array parsed correctly")
            print(f"   Result: {result}")
        else:
            print("❌ Test 1 FAILED: Valid JSON array not parsed correctly")
            print(f"   Expected: list with 2 items, Got: {type(result)} with {len(result) if isinstance(result, list) else 'N/A'} items")
            
        # Test 2: Empty/None input
        result = manager._parse_consolidated_details(None)
        if isinstance(result, list) and len(result) == 0:
            print("✅ Test 2 PASSED: None input returns empty list")
        else:
            print("❌ Test 2 FAILED: None input should return empty list")
            
        # Test 3: Invalid JSON
        result = manager._parse_consolidated_details('invalid json')
        if isinstance(result, list) and len(result) == 0:
            print("✅ Test 3 PASSED: Invalid JSON returns empty list")
        else:
            print("❌ Test 3 FAILED: Invalid JSON should return empty list")
            
        # Test 4: Non-array JSON
        result = manager._parse_consolidated_details('{"not": "an array"}')
        if isinstance(result, list) and len(result) == 0:
            print("✅ Test 4 PASSED: Non-array JSON returns empty list")
        else:
            print("❌ Test 4 FAILED: Non-array JSON should return empty list")
            
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_consolidated_details():
    """Test actual database consolidated_details retrieval"""
    print("\n🗄️ TESTING DATABASE CONSOLIDATED DETAILS RETRIEVAL:")
    print("-" * 50)
    
    try:
        db_path = Path('data/templar_payroll_auditor.db')
        if not db_path.exists():
            print("⚠️ Database not found, skipping database test")
            return True
            
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check if there are any consolidated entries
        cursor.execute("""
            SELECT id, employee_id, item_label, consolidated_details
            FROM comparison_results 
            WHERE change_type = 'CONSOLIDATED' 
            AND consolidated_details IS NOT NULL
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        
        if results:
            print(f"📊 Found {len(results)} consolidated entries in database:")
            
            for i, (id_val, emp_id, item_label, consolidated_details) in enumerate(results, 1):
                print(f"\n   Entry {i}:")
                print(f"   ID: {id_val}")
                print(f"   Employee: {emp_id}")
                print(f"   Item: {item_label}")
                print(f"   Consolidated Details Type: {type(consolidated_details)}")
                
                if consolidated_details:
                    print(f"   Raw Data: {consolidated_details[:100]}...")
                    
                    # Try to parse it
                    try:
                        parsed = json.loads(consolidated_details)
                        if isinstance(parsed, list):
                            print(f"   ✅ Successfully parsed as list with {len(parsed)} items")
                        else:
                            print(f"   ⚠️ Parsed but not a list: {type(parsed)}")
                    except json.JSONDecodeError:
                        print(f"   ❌ Failed to parse as JSON")
                else:
                    print(f"   ⚠️ No consolidated details data")
                    
        else:
            print("⚠️ No consolidated entries found in database")
            print("   This might be expected if no consolidation has occurred yet")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_get_comparison_results_method():
    """Test the actual get_comparison_results method"""
    print("\n🔍 TESTING get_comparison_results METHOD:")
    print("-" * 50)
    
    try:
        sys.path.append('core')
        from phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug=False)  # Reduce noise
        
        # Try to get latest comparison results
        result = manager.get_latest_comparison_results()
        
        if result and result.get('success'):
            data = result.get('data', [])
            print(f"✅ Successfully retrieved {len(data)} comparison results")
            
            # Check for consolidated entries
            consolidated_entries = [item for item in data if item.get('change_type') == 'CONSOLIDATED']
            
            if consolidated_entries:
                print(f"📊 Found {len(consolidated_entries)} consolidated entries:")
                
                for i, entry in enumerate(consolidated_entries[:3], 1):  # Check first 3
                    consolidated_details = entry.get('consolidated_details')
                    print(f"\n   Consolidated Entry {i}:")
                    print(f"   Employee: {entry.get('employee_id')}")
                    print(f"   Item: {entry.get('item_label')}")
                    print(f"   Consolidated Details Type: {type(consolidated_details)}")
                    
                    if isinstance(consolidated_details, list):
                        print(f"   ✅ consolidated_details is properly parsed as list with {len(consolidated_details)} items")
                    elif isinstance(consolidated_details, str):
                        print(f"   ❌ consolidated_details is still a string (not parsed)")
                        print(f"   Raw: {consolidated_details[:100]}...")
                    else:
                        print(f"   ⚠️ consolidated_details is {type(consolidated_details)}: {consolidated_details}")
                        
            else:
                print("⚠️ No consolidated entries found in results")
                print("   This might be expected if no consolidation has occurred")
                
        else:
            error = result.get('error', 'Unknown error') if result else 'No result returned'
            print(f"❌ Failed to get comparison results: {error}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Method test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 CONSOLIDATED DETAILS JSON PARSING FIX VERIFICATION")
    print("=" * 70)
    
    results = []
    
    # Test 1: Method functionality
    results.append(test_consolidated_details_parsing())
    
    # Test 2: Database data
    results.append(test_database_consolidated_details())
    
    # Test 3: Full method integration
    results.append(test_get_comparison_results_method())
    
    # Summary
    print("\n📋 TEST SUMMARY")
    print("=" * 30)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ FIX VERIFIED:")
        print("   1. _parse_consolidated_details method works correctly")
        print("   2. Database consolidated_details can be parsed")
        print("   3. get_comparison_results returns proper arrays")
        print("\n🚀 The spinning UI issue should now be FIXED!")
        print("   consolidated_details.filter() will now work properly")
    else:
        print("⚠️ Some tests failed - please review the output above")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
