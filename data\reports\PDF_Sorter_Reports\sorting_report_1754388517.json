{"report_type": "PDF_Sorting_Report", "generated_at": "2025-08-05 10:08:37", "source_tab": "pdf_sorter", "result": {"success": true, "output_path": "C:\\THE PAYROLL AUDITOR\\data\\reports\\PDF_Sorter_Reports\\MN PAYSLIPS JUL 2025 FINAL_sorted_1754388510.pdf", "output_filename": "MN PAYSLIPS JUL 2025 FINAL_sorted_1754388510.pdf", "total_payslips": 2962, "processing_time": 156.7, "sort_criteria": "Section (Ascending)", "sort_config": {"primary_sort": "section", "secondary_sort": "", "tertiary_sort": "", "sort_order": "ascending"}, "preview_items": [{"position": 1, "employee_no": "COP0209", "employee_name": "", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 1}, {"position": 2, "employee_no": "COP0361", "employee_name": "<PERSON><PERSON>i-Arkoh  R.   K.", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 2}, {"position": 3, "employee_no": "COP0388", "employee_name": "<PERSON><PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 3}, {"position": 4, "employee_no": "COP0540", "employee_name": "<PERSON><PERSON><PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 4}, {"position": 5, "employee_no": "COP0868", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 5}, {"position": 6, "employee_no": "COP1108", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 6}, {"position": 7, "employee_no": "COP1240", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 7}, {"position": 8, "employee_no": "COP1253", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 8}, {"position": 9, "employee_no": "COP1425", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 9}, {"position": 10, "employee_no": "COP1438", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 10}, {"position": 11, "employee_no": "COP1513", "employee_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 11}, {"position": 12, "employee_no": "COP1638", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 12}, {"position": 13, "employee_no": "COP1785", "employee_name": "<PERSON><PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 13}, {"position": 14, "employee_no": "COP1895", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 14}, {"position": 15, "employee_no": "COP1919", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 15}, {"position": 16, "employee_no": "COP1992", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 16}, {"position": 17, "employee_no": "COP2074", "employee_name": "<PERSON><PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 17}, {"position": 18, "employee_no": "COP2100", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 18}, {"position": 19, "employee_no": "COP2171", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 19}, {"position": 20, "employee_no": "COP2201", "employee_name": "<PERSON>", "primary_value": "<PERSON><PERSON><PERSON>", "page_number": 20}], "stats": {"total_pages": 2962, "processed_pages": 2962, "extracted_payslips": 2962, "failed_extractions": 0, "start_time": 1754388360.9804828, "current_stage": "Finalizing results..."}, "timestamp": 1754388517, "integration_version": "1.0.0", "sorted_at": "2025-08-05 10:08:37"}}