#!/usr/bin/env python3
"""
Generate Employee-Based Report with Real Data
Uses the new Final Report system with real payroll data from the database
"""

import sys
import os
import sqlite3
import json
from datetime import datetime
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def load_real_payroll_data():
    """Load real payroll data from the database"""
    print("📊 LOADING REAL PAYROLL DATA")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return None, None
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session_result = cursor.fetchone()
        
        if not current_session_result:
            print("❌ No current session found")
            return None, None
        
        session_id = current_session_result[0]
        print(f"✅ Using session: {session_id}")
        
        # Load comparison results with dictionary filtering
        cursor.execute('''
            SELECT cr.employee_id, cr.employee_name, cr.section_name, cr.item_label,
                   cr.previous_value, cr.current_value, cr.change_type, cr.priority,
                   cr.numeric_difference, cr.percentage_change
            FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            LEFT JOIN dictionary_sections ds ON di.section_id = ds.id AND cr.section_name = ds.section_name
            WHERE cr.session_id = ?
            AND (
                -- Include items that are marked to be included in reports
                (di.include_in_report = 1 OR di.include_in_report IS NULL)
                AND
                -- Apply change detection filtering based on change type
                (
                    (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                    (cr.change_type = 'INCREASED' AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                    (cr.change_type = 'DECREASED' AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                    (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                    (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1 OR di.include_no_change IS NULL)) OR
                    (cr.change_type NOT IN ('NEW', 'INCREASED', 'DECREASED', 'REMOVED', 'NO_CHANGE'))
                )
            )
            ORDER BY cr.employee_name, cr.section_name, cr.item_label
            LIMIT 50
        ''', (session_id,))
        
        comparison_results = cursor.fetchall()
        print(f"✅ Loaded {len(comparison_results)} comparison results")
        
        # Load current period data for NEW employee detection
        cursor.execute('''
            SELECT DISTINCT employee_id, employee_name, section_name
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
            ORDER BY employee_name
        ''', (session_id,))
        
        current_employees = cursor.fetchall()
        
        # Load previous period data for REMOVED employee detection
        cursor.execute('''
            SELECT DISTINCT employee_id, employee_name, section_name
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'previous'
            ORDER BY employee_name
        ''', (session_id,))
        
        previous_employees = cursor.fetchall()
        
        print(f"✅ Current period: {len(current_employees)} employees")
        print(f"✅ Previous period: {len(previous_employees)} employees")
        
        conn.close()
        
        return {
            'session_id': session_id,
            'comparison_results': comparison_results,
            'current_employees': current_employees,
            'previous_employees': previous_employees
        }
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None, None

def apply_business_rules(data):
    """Apply business rules to real data (simplified version)"""
    print("\n🧠 APPLYING BUSINESS RULES")
    print("=" * 50)
    
    # Convert comparison results to format expected by business rules
    changes = []
    for row in data['comparison_results']:
        change = {
            'employee_id': row[0],
            'employee_name': row[1],
            'section_name': row[2],
            'item_name': row[3],
            'previous_value': row[4] or '0',
            'current_value': row[5] or '0',
            'change_type': row[6],
            'priority': row[7],
            'department': row[2]  # Using section as department for now
        }
        changes.append(change)
    
    # Detect NEW employees (in current but not in previous)
    current_emp_ids = {emp[0] for emp in data['current_employees']}
    previous_emp_ids = {emp[0] for emp in data['previous_employees']}
    
    new_employee_ids = current_emp_ids - previous_emp_ids
    new_employees = [emp for emp in data['current_employees'] if emp[0] in new_employee_ids]
    
    # Detect REMOVED employees (in previous but not in current)
    removed_employee_ids = previous_emp_ids - current_emp_ids
    removed_employees = [emp for emp in data['previous_employees'] if emp[0] in removed_employee_ids]
    
    print(f"✅ Processed {len(changes)} changes")
    print(f"✅ Detected {len(new_employees)} NEW employees")
    print(f"✅ Detected {len(removed_employees)} REMOVED employees")
    
    # Group changes by employee
    employee_groups = {}
    for change in changes:
        emp_id = change['employee_id']
        if emp_id not in employee_groups:
            employee_groups[emp_id] = {
                'employee_id': emp_id,
                'employee_name': change['employee_name'],
                'department': change['department'],
                'changes': []
            }
        employee_groups[emp_id]['changes'].append(change)
    
    # Generate narration for each change
    for emp_id, emp_data in employee_groups.items():
        for i, change in enumerate(emp_data['changes']):
            change['narration'] = generate_change_narration(change)
            change['number'] = i + 1
    
    return {
        'employee_groups': employee_groups,
        'new_employees': new_employees,
        'removed_employees': removed_employees,
        'total_changes': len(changes),
        'high_priority': len([c for c in changes if c['priority'] == 'HIGH']),
        'moderate_priority': len([c for c in changes if c['priority'] == 'MODERATE']),
        'low_priority': len([c for c in changes if c['priority'] == 'LOW'])
    }

def generate_change_narration(change):
    """Generate narration according to specification"""
    item = change['item_name']
    prev_val = change['previous_value']
    curr_val = change['current_value']
    change_type = change['change_type']
    
    current_month = datetime.now().strftime('%B %Y').upper()
    
    try:
        prev_num = float(prev_val) if prev_val else 0
        curr_num = float(curr_val) if curr_val else 0
        
        if change_type == 'INCREASED':
            diff = curr_num - prev_num
            return f"{item} increased from {prev_num:.2f} to {curr_num:.2f} in {current_month} (increase of {diff:.2f})"
        elif change_type == 'DECREASED':
            diff = prev_num - curr_num
            return f"{item} decreased from {prev_num:.2f} to {curr_num:.2f} in {current_month} (decrease of {diff:.2f})"
        elif change_type == 'NEW':
            return f"{item} of {curr_num:.2f} was added to Payslip for {current_month}"
        elif change_type == 'REMOVED':
            return f"{item} of {prev_num:.2f} was removed from Payslip for {current_month}"
        else:
            return f"{item} changed from {prev_val} to {curr_val} in {current_month}"
    except:
        return f"{item} changed from {prev_val} to {curr_val} in {current_month}"

def create_employee_based_word_report(processed_data):
    """Create Word document following exact specification format"""
    print("\n📄 CREATING EMPLOYEE-BASED WORD REPORT")
    print("=" * 50)
    
    # Create document
    doc = Document()
    
    # Title (Calibri Headings 26)
    current_date = datetime.now()
    month_year = current_date.strftime('%B %Y').upper()
    title = doc.add_heading(f'PAYROLL AUDIT REPORT: {month_year}', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Report Information and Executive Summary Table
    table = doc.add_table(rows=5, cols=2)
    table.style = 'Table Grid'
    
    # Headers
    table.cell(0, 0).text = 'Report Information'
    table.cell(0, 1).text = 'Executive Summary'
    
    # Report Information (Left column)
    table.cell(1, 0).text = f'Report Period: {month_year}'
    table.cell(2, 0).text = f'Generated at: {current_date.strftime("%Y-%m-%d %H:%M")}'
    table.cell(3, 0).text = 'Generated By: System Administrator'
    table.cell(4, 0).text = 'Designation: Payroll Auditor'
    
    # Executive Summary (Right column)
    significant_changes = processed_data['high_priority'] + processed_data['moderate_priority']
    table.cell(1, 1).text = f'Significant Changes Detected: {significant_changes}'
    table.cell(2, 1).text = f'HIGH Priority Changes: {processed_data["high_priority"]}'
    table.cell(3, 1).text = f'MODERATE Priority Changes: {processed_data["moderate_priority"]}'
    table.cell(4, 1).text = f'LOW Priority Changes: {processed_data["low_priority"]}'
    
    # Finding and Observations
    doc.add_heading('Finding and Observations', level=1)
    
    # Employee-based findings
    for emp_id, emp_data in processed_data['employee_groups'].items():
        if emp_data['changes']:
            # Employee header
            header = f"{emp_data['employee_id']}: {emp_data['employee_name']} – {emp_data['department']}"
            emp_para = doc.add_paragraph()
            emp_para.add_run(header).bold = True
            
            # Numbered changes
            for change in emp_data['changes']:
                change_para = doc.add_paragraph()
                change_para.add_run(f"{change['number']}. {change['narration']}")
                change_para.style = 'List Number'
            
            # Add spacing
            doc.add_paragraph()
    
    # NEW EMPLOYEES section
    if processed_data['new_employees']:
        doc.add_heading('NEW EMPLOYEES', level=1)
        desc_para = doc.add_paragraph()
        desc_para.add_run(f'The following Employees were added to {month_year} Payroll:')
        
        for emp in processed_data['new_employees']:
            emp_para = doc.add_paragraph()
            emp_para.add_run(f'• {emp[0]}: {emp[1]} – {emp[2]}')
            emp_para.style = 'List Bullet'
    
    # REMOVED EMPLOYEES section
    if processed_data['removed_employees']:
        doc.add_heading('REMOVED EMPLOYEES', level=1)
        desc_para = doc.add_paragraph()
        desc_para.add_run(f'The following Employees were removed from {month_year} Payroll:')
        
        for emp in processed_data['removed_employees']:
            emp_para = doc.add_paragraph()
            emp_para.add_run(f'• {emp[0]}: {emp[1]} – {emp[2]}')
            emp_para.style = 'List Bullet'
    
    # Footer
    footer_para = doc.add_paragraph()
    footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    footer_para.add_run(f'Page 1 | TEMPLAR PAYROLL AUDITOR | All Rights Reserved © {current_date.year}')
    
    # Save document
    timestamp = current_date.strftime('%Y%m%d_%H%M%S')
    filename = f'Employee_Based_Report_Real_Data_{timestamp}.docx'
    doc.save(filename)
    
    print(f"✅ Report saved: {filename}")
    return filename

def main():
    """Generate employee-based report with real data"""
    print("🎯 GENERATING EMPLOYEE-BASED REPORT WITH REAL DATA")
    print("=" * 60)
    print("Using the new Final Report system with actual payroll data")
    print("=" * 60)
    
    try:
        # Load real data
        real_data = load_real_payroll_data()
        if not real_data:
            print("❌ Failed to load real data")
            return False
        
        # Apply business rules
        processed_data = apply_business_rules(real_data)
        
        # Generate Word report
        filename = create_employee_based_word_report(processed_data)
        
        print(f"\n🎉 SUCCESS!")
        print(f"📄 Employee-based report generated: {filename}")
        print(f"📊 Report contains:")
        print(f"   • {len(processed_data['employee_groups'])} employees with changes")
        print(f"   • {processed_data['total_changes']} total changes")
        print(f"   • {len(processed_data['new_employees'])} NEW employees")
        print(f"   • {len(processed_data['removed_employees'])} REMOVED employees")
        print(f"   • Priority breakdown: {processed_data['high_priority']} HIGH, {processed_data['moderate_priority']} MODERATE, {processed_data['low_priority']} LOW")
        print(f"\n✅ Report follows exact specification format!")
        print(f"💡 Open the file to review the actual employee-based report")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    main()
