#!/usr/bin/env python3
"""
Test all report generation entry points to verify dictionary filtering compliance
"""

import sqlite3
import os
import subprocess
import sys
import json

def test_all_report_generation_entry_points():
    """Test every report generation function for dictionary filtering compliance"""
    
    print('=== TESTING ALL REPORT GENERATION ENTRY POINTS ===\n')
    
    # Get current session
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print('❌ Database file not found')
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    
    if not session_result:
        print('❌ No audit sessions found')
        return False
    
    session_id = session_result[0]
    print(f'Using session: {session_id}')
    
    # Get baseline counts
    cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
    total_results = cursor.fetchone()[0]
    
    cursor.execute('''
        SELECT COUNT(*)
        FROM comparison_results cr
        LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
        LEFT JOIN dictionary_sections ds ON di.section_id = ds.id AND cr.section_name = ds.section_name
        WHERE cr.session_id = ?
        AND (
            (di.include_in_report = 1 OR di.include_in_report IS NULL)
            AND
            (
                (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                (cr.change_type = 'INCREASED' AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                (cr.change_type = 'DECREASED' AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1 OR di.include_no_change IS NULL)) OR
                (cr.change_type NOT IN ('NEW', 'INCREASED', 'DECREASED', 'REMOVED', 'NO_CHANGE'))
            )
        )
    ''', (session_id,))
    
    expected_filtered_count = cursor.fetchone()[0]
    
    print(f'Total comparison results: {total_results}')
    print(f'Expected filtered results: {expected_filtered_count}')
    print(f'Filtering effectiveness: {((total_results - expected_filtered_count) / total_results * 100):.1f}% reduction\n')
    
    # Test all report generation entry points
    test_cases = [
        {
            'name': 'Report Generation Bridge',
            'script': 'core/report_generation_bridge.py',
            'args': ['generate-report', session_id],
            'expected_filtering': True
        },
        {
            'name': 'Advanced Report Generator - Employee',
            'script': 'core/advanced_report_generator.py',
            'args': ['generate-employee', session_id],
            'expected_filtering': True
        },
        {
            'name': 'Advanced Report Generator - Item',
            'script': 'core/advanced_report_generator.py',
            'args': ['generate-item', session_id],
            'expected_filtering': True
        },
        {
            'name': 'Advanced Report Generator - Both',
            'script': 'core/advanced_report_generator.py',
            'args': ['generate-both', session_id],
            'expected_filtering': True
        },
        {
            'name': 'Final Corrected Report',
            'script': 'generate_final_corrected_report.py',
            'args': [session_id],
            'expected_filtering': True
        },
        {
            'name': 'Corrected Employee Report',
            'script': 'generate_corrected_employee_report.py',
            'args': [session_id],
            'expected_filtering': True
        },
        {
            'name': 'Real Employee Report',
            'script': 'generate_real_employee_report.py',
            'args': [session_id],
            'expected_filtering': True
        },
        {
            'name': 'Final Specification Compliant Report',
            'script': 'generate_final_specification_compliant_report.py',
            'args': [session_id],
            'expected_filtering': True
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"🧪 Testing: {test_case['name']}")
        
        if not os.path.exists(test_case['script']):
            print(f"   ⚠️  Script not found: {test_case['script']}")
            results.append({
                'name': test_case['name'],
                'status': 'SCRIPT_NOT_FOUND',
                'filtering_applied': False
            })
            continue
        
        try:
            # Run the script
            result = subprocess.run(
                [sys.executable, test_case['script']] + test_case['args'],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                output = result.stdout.lower()
                
                # Check for filtering evidence
                filtering_indicators = [
                    'dictionary filtering',
                    'filtered',
                    'after filtering',
                    'dictionary manager',
                    'include_in_report'
                ]
                
                filtering_applied = any(indicator in output for indicator in filtering_indicators)
                
                # Try to extract count information
                count_info = 'No count information'
                if 'found' in output and 'results' in output:
                    lines = output.split('\n')
                    for line in lines:
                        if 'found' in line and ('results' in line or 'changes' in line):
                            count_info = line.strip()
                            break
                
                print(f"   ✅ Success: {count_info}")
                if filtering_applied:
                    print(f"   ✅ Dictionary filtering applied")
                else:
                    print(f"   ⚠️  No clear evidence of dictionary filtering")
                
                results.append({
                    'name': test_case['name'],
                    'status': 'SUCCESS',
                    'filtering_applied': filtering_applied,
                    'count_info': count_info
                })
                
            else:
                error_msg = result.stderr[:200] if result.stderr else 'Unknown error'
                print(f"   ❌ Failed: {error_msg}")
                results.append({
                    'name': test_case['name'],
                    'status': 'FAILED',
                    'filtering_applied': False,
                    'error': error_msg
                })
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ Timeout")
            results.append({
                'name': test_case['name'],
                'status': 'TIMEOUT',
                'filtering_applied': False
            })
        except Exception as e:
            print(f"   ❌ Exception: {str(e)[:100]}")
            results.append({
                'name': test_case['name'],
                'status': 'EXCEPTION',
                'filtering_applied': False,
                'error': str(e)
            })
        
        print()
    
    # Summary
    print('📋 SUMMARY OF REPORT GENERATION ENTRY POINTS:')
    print('=' * 60)
    
    total_tests = len(results)
    successful_tests = len([r for r in results if r['status'] == 'SUCCESS'])
    filtering_applied_tests = len([r for r in results if r.get('filtering_applied', False)])
    
    print(f'Total entry points tested: {total_tests}')
    print(f'Successful executions: {successful_tests}')
    print(f'Dictionary filtering applied: {filtering_applied_tests}')
    print(f'Filtering compliance rate: {(filtering_applied_tests / total_tests * 100):.1f}%')
    
    print('\nDetailed Results:')
    for result in results:
        status_icon = {
            'SUCCESS': '✅',
            'FAILED': '❌',
            'TIMEOUT': '⏰',
            'EXCEPTION': '💥',
            'SCRIPT_NOT_FOUND': '📄'
        }.get(result['status'], '❓')
        
        filtering_icon = '🔍' if result.get('filtering_applied', False) else '⚠️'
        
        print(f"  {status_icon} {filtering_icon} {result['name']}: {result['status']}")
        if 'count_info' in result:
            print(f"      {result['count_info']}")
        if 'error' in result:
            print(f"      Error: {result['error'][:100]}")
    
    conn.close()
    
    return filtering_applied_tests == total_tests

if __name__ == '__main__':
    success = test_all_report_generation_entry_points()
    sys.exit(0 if success else 1)
