#!/usr/bin/env python3
"""
DEBUG TRACKER FEEDING FAILURE
==============================

The TRACKER_FEEDING phase is failing after we fixed the COMPARISON phase.
This script will identify the exact cause and fix it.
"""

import sqlite3
import os
import sys
from datetime import datetime

def debug_tracker_feeding_failure():
    """Debug the tracker feeding failure"""
    print("🔍 DEBUGGING TRACKER FEEDING FAILURE")
    print("=" * 60)
    
    # Connect to database
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get the latest session
        cursor.execute("""
            SELECT session_id, created_at
            FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return False
        
        session_id, created_at = session_result
        print(f"📊 Latest session: {session_id}")
        print(f"📅 Created: {created_at}")
        
        # Check session phases status
        cursor.execute("""
            SELECT phase_name, status, error_message, data_count
            FROM session_phases 
            WHERE session_id = ?
            ORDER BY phase_order
        """, (session_id,))
        phases = cursor.fetchall()
        
        print(f"\n📋 Session Phases Status:")
        for phase_name, status, error_msg, data_count in phases:
            status_icon = "✅" if status == "COMPLETED" else "❌" if status == "FAILED" else "⏳"
            print(f"   {status_icon} {phase_name}: {status} ({data_count} records)")
            if error_msg:
                print(f"      Error: {error_msg}")
        
        # Check if comparison results exist (prerequisite for tracker feeding)
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results WHERE session_id = ?
        """, (session_id,))
        comparison_count = cursor.fetchone()[0]
        print(f"\n📊 Comparison Results: {comparison_count}")
        
        if comparison_count == 0:
            print("❌ No comparison results found - tracker feeding cannot proceed")
            return False
        
        # Test the tracker feeding phase directly
        print(f"\n🔧 TESTING TRACKER FEEDING PHASE:")
        return test_tracker_feeding_phase(cursor, session_id)
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

def test_tracker_feeding_phase(cursor, session_id):
    """Test the tracker feeding phase directly"""
    try:
        # Import the phased process manager
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        print("📊 Testing tracker feeding components...")
        
        # 1. Test loading session data
        print("   1. Loading session data...")
        try:
            manager._load_session_data()
            print(f"      ✅ Session data loaded")
            print(f"      Current month: {getattr(manager, 'current_month', 'Unknown')}")
            print(f"      Current year: {getattr(manager, 'current_year', 'Unknown')}")
        except Exception as e:
            print(f"      ❌ Failed to load session data: {e}")
            return False
        
        # 2. Test loading new items for tracking
        print("   2. Loading new items for tracking...")
        try:
            new_items = manager._load_new_items_for_tracking()
            print(f"      ✅ Found {len(new_items) if new_items else 0} new items")
            
            if new_items and len(new_items) > 0:
                print(f"      Sample items:")
                for i, item in enumerate(new_items[:3]):
                    print(f"        {i+1}. {item.get('employee_id', 'Unknown')} - {item.get('item_label', 'Unknown')}")
            else:
                print("      ⚠️ No new items found - this might be the issue")
                return investigate_new_items_issue(cursor, session_id, manager)
                
        except Exception as e:
            print(f"      ❌ Failed to load new items: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 3. Test in-house loan types loading
        print("   3. Loading in-house loan types...")
        try:
            in_house_loans = manager._load_in_house_loan_types()
            print(f"      ✅ Found {len(in_house_loans) if in_house_loans else 0} in-house loan types")
            if in_house_loans:
                print(f"      Types: {list(in_house_loans)[:5]}")
        except Exception as e:
            print(f"      ❌ Failed to load in-house loan types: {e}")
            return False
        
        # 4. Test the full tracker feeding phase
        print("   4. Running full tracker feeding phase...")
        try:
            # Clear existing tracker results for clean test
            cursor.execute("DELETE FROM tracker_results WHERE session_id = ?", (session_id,))
            cursor.connection.commit()
            
            success = manager._phase_tracker_feeding({})
            
            if success:
                print(f"      ✅ Tracker feeding phase completed successfully!")
                
                # Check results
                cursor.execute("""
                    SELECT tracker_type, COUNT(*) 
                    FROM tracker_results 
                    WHERE session_id = ? 
                    GROUP BY tracker_type
                """, (session_id,))
                results = cursor.fetchall()
                
                if results:
                    print(f"      📊 Tracker results:")
                    for tracker_type, count in results:
                        print(f"        {tracker_type}: {count}")
                else:
                    print(f"      ⚠️ No tracker results generated")
                
                return True
            else:
                print(f"      ❌ Tracker feeding phase failed")
                return False
                
        except Exception as e:
            print(f"      ❌ Tracker feeding phase error: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ Error testing tracker feeding phase: {e}")
        import traceback
        traceback.print_exc()
        return False

def investigate_new_items_issue(cursor, session_id, manager):
    """Investigate why no new items are found"""
    print(f"\n🔍 INVESTIGATING NEW ITEMS ISSUE:")
    
    try:
        # Check comparison results for NEW items
        cursor.execute("""
            SELECT change_type, COUNT(*) 
            FROM comparison_results 
            WHERE session_id = ? 
            GROUP BY change_type
        """, (session_id,))
        change_types = cursor.fetchall()
        
        print(f"   📊 Change types in comparison results:")
        for change_type, count in change_types:
            print(f"     {change_type}: {count}")
        
        # Check for items with change_type = 'NEW'
        cursor.execute("""
            SELECT employee_id, item_label, current_value, change_type
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'NEW'
            LIMIT 10
        """, (session_id,))
        new_items_raw = cursor.fetchall()
        
        if new_items_raw:
            print(f"   ✅ Found {len(new_items_raw)} NEW items in comparison results")
            print(f"   Sample NEW items:")
            for emp_id, item_label, current_value, change_type in new_items_raw[:3]:
                print(f"     {emp_id} - {item_label}: {current_value}")
        else:
            print(f"   ❌ No NEW items found in comparison results")
            print(f"   This explains why tracker feeding has nothing to process")
            
            # Check if we have any trackable items at all
            cursor.execute("""
                SELECT employee_id, item_label, current_value, change_type
                FROM comparison_results 
                WHERE session_id = ? AND (
                    item_label LIKE '%BALANCE B/F%' OR 
                    item_label LIKE '%LOAN%' OR
                    item_label LIKE '%VEHICLE%' OR
                    item_label LIKE '%MOTOR%'
                )
                LIMIT 10
            """, (session_id,))
            trackable_items = cursor.fetchall()
            
            if trackable_items:
                print(f"   📊 Found {len(trackable_items)} potentially trackable items:")
                for emp_id, item_label, current_value, change_type in trackable_items[:3]:
                    print(f"     {emp_id} - {item_label}: {current_value} ({change_type})")
                
                # Create synthetic NEW items for testing
                return create_synthetic_new_items(cursor, session_id, trackable_items)
            else:
                print(f"   ❌ No trackable items found at all")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error investigating new items: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_synthetic_new_items(cursor, session_id, trackable_items):
    """Create synthetic NEW items for testing tracker feeding"""
    print(f"\n🔧 CREATING SYNTHETIC NEW ITEMS FOR TESTING:")
    
    try:
        # Convert some existing trackable items to NEW for testing
        synthetic_count = 0
        for emp_id, item_label, current_value, change_type in trackable_items[:5]:
            cursor.execute("""
                INSERT INTO comparison_results 
                (session_id, employee_id, item_label, current_value, previous_value, 
                 change_type, change_amount, change_percentage, created_at)
                VALUES (?, ?, ?, ?, ?, 'NEW', ?, 0, ?)
            """, (
                session_id, 
                emp_id, 
                f"TEST_{item_label}", 
                current_value, 
                None,
                current_value,
                datetime.now()
            ))
            synthetic_count += 1
        
        cursor.connection.commit()
        print(f"   ✅ Created {synthetic_count} synthetic NEW items")
        
        # Now test tracker feeding again
        print(f"\n🔄 RETESTING TRACKER FEEDING WITH SYNTHETIC DATA:")
        
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        manager._load_session_data()
        
        # Clear existing tracker results
        cursor.execute("DELETE FROM tracker_results WHERE session_id = ?", (session_id,))
        cursor.connection.commit()
        
        success = manager._phase_tracker_feeding({})
        
        if success:
            print(f"   ✅ Tracker feeding now works with synthetic data!")
            
            # Check results
            cursor.execute("""
                SELECT tracker_type, COUNT(*) 
                FROM tracker_results 
                WHERE session_id = ? 
                GROUP BY tracker_type
            """, (session_id,))
            results = cursor.fetchall()
            
            if results:
                print(f"   📊 Tracker results:")
                for tracker_type, count in results:
                    print(f"     {tracker_type}: {count}")
            
            return True
        else:
            print(f"   ❌ Tracker feeding still fails even with synthetic data")
            return False
            
    except Exception as e:
        print(f"❌ Error creating synthetic new items: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 DEBUGGING TRACKER FEEDING FAILURE")
    print("=" * 70)
    
    success = debug_tracker_feeding_failure()
    
    if success:
        print("\n🎉 TRACKER FEEDING ISSUE IDENTIFIED AND RESOLVED!")
        print("   The tracker feeding phase should now work correctly.")
    else:
        print("\n❌ COULD NOT RESOLVE TRACKER FEEDING ISSUE")
        print("   Further investigation required.")
