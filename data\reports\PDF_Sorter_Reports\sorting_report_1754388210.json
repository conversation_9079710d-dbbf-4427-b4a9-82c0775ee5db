{"report_type": "PDF_Sorting_Report", "generated_at": "2025-08-05 10:03:30", "source_tab": "pdf_sorter", "result": {"success": true, "output_path": "C:\\THE PAYROLL AUDITOR\\data\\reports\\PDF_Sorter_Reports\\HQ MN PAYSLIPS AUG 2025_sorted_1754388210.pdf", "output_filename": "HQ MN PAYSLIPS AUG 2025_sorted_1754388210.pdf", "total_payslips": 211, "processing_time": 7.62, "sort_criteria": "Section (Ascending)", "sort_config": {"primary_sort": "section", "secondary_sort": "", "tertiary_sort": "", "sort_order": "ascending"}, "preview_items": [{"position": 1, "employee_no": "COP0248", "employee_name": "<PERSON><PERSON><PERSON><PERSON>", "primary_value": "Headquarters", "page_number": 1}, {"position": 2, "employee_no": "COP0457", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 2}, {"position": 3, "employee_no": "COP0587", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 3}, {"position": 4, "employee_no": "COP0594", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 4}, {"position": 5, "employee_no": "COP0601", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 5}, {"position": 6, "employee_no": "COP0622", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 6}, {"position": 7, "employee_no": "COP0624", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 7}, {"position": 8, "employee_no": "COP0681", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 8}, {"position": 9, "employee_no": "COP0692", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 9}, {"position": 10, "employee_no": "COP0700", "employee_name": "<PERSON><PERSON>", "primary_value": "Headquarters", "page_number": 10}, {"position": 11, "employee_no": "COP0701", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 11}, {"position": 12, "employee_no": "COP0746", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 12}, {"position": 13, "employee_no": "COP0751", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 13}, {"position": 14, "employee_no": "COP0754", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 14}, {"position": 15, "employee_no": "COP0765", "employee_name": "<PERSON>-<PERSON><PERSON>", "primary_value": "Headquarters", "page_number": 15}, {"position": 16, "employee_no": "COP0796", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 16}, {"position": 17, "employee_no": "COP0825", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 17}, {"position": 18, "employee_no": "COP0830", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 18}, {"position": 19, "employee_no": "COP0873", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 19}, {"position": 20, "employee_no": "COP0883", "employee_name": "<PERSON>", "primary_value": "Headquarters", "page_number": 20}], "stats": {"total_pages": 211, "processed_pages": 211, "extracted_payslips": 211, "failed_extractions": 0, "start_time": 1754388202.8157282, "current_stage": "Finalizing results..."}, "timestamp": 1754388210, "integration_version": "1.0.0", "sorted_at": "2025-08-05 10:03:30"}}