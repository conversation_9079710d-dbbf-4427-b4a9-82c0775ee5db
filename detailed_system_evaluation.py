#!/usr/bin/env python3
"""
Detailed evaluation of the three working report systems to determine the best one
"""

import os
import sqlite3
import subprocess
import sys
import json
from datetime import datetime

def evaluate_advanced_report_generator():
    """Evaluate Advanced Report Generator system"""
    
    print('🔍 EVALUATING: Advanced Report Generator')
    print('=' * 50)
    
    # Get session for testing
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_id = cursor.fetchone()[0]
    conn.close()
    
    evaluation = {
        'name': 'Advanced Report Generator',
        'file': 'core/advanced_report_generator.py',
        'strengths': [],
        'weaknesses': [],
        'features': {},
        'test_results': {},
        'score': 0
    }
    
    # Test functionality
    print('🧪 Testing functionality...')
    
    # Test employee report
    try:
        result = subprocess.run(
            [sys.executable, 'core/advanced_report_generator.py', 'generate-employee', session_id],
            capture_output=True, text=True, timeout=30
        )
        if result.returncode == 0:
            output = json.loads(result.stdout)
            evaluation['test_results']['employee_report'] = {
                'status': 'success',
                'files_created': len(output.get('files', {})),
                'processed_changes': output.get('processed_changes', 0),
                'filtering_stats': output.get('filtering_stats', {})
            }
            evaluation['strengths'].append('Employee-based reports working')
            evaluation['score'] += 20
        else:
            evaluation['test_results']['employee_report'] = {'status': 'failed', 'error': result.stderr}
            evaluation['weaknesses'].append('Employee report generation failed')
    except Exception as e:
        evaluation['test_results']['employee_report'] = {'status': 'error', 'error': str(e)}
        evaluation['weaknesses'].append(f'Employee report test error: {str(e)[:50]}')
    
    # Test item report
    try:
        result = subprocess.run(
            [sys.executable, 'core/advanced_report_generator.py', 'generate-item', session_id],
            capture_output=True, text=True, timeout=30
        )
        if result.returncode == 0:
            output = json.loads(result.stdout)
            evaluation['test_results']['item_report'] = {
                'status': 'success',
                'files_created': len(output.get('files', {})),
                'processed_changes': output.get('processed_changes', 0)
            }
            evaluation['strengths'].append('Item-based reports working')
            evaluation['score'] += 20
        else:
            evaluation['test_results']['item_report'] = {'status': 'failed', 'error': result.stderr}
            evaluation['weaknesses'].append('Item report generation failed')
    except Exception as e:
        evaluation['test_results']['item_report'] = {'status': 'error', 'error': str(e)}
        evaluation['weaknesses'].append(f'Item report test error: {str(e)[:50]}')
    
    # Analyze features
    with open('core/advanced_report_generator.py', 'r') as f:
        content = f.read()
    
    evaluation['features'] = {
        'dictionary_filtering': 'dictionary_items' in content and 'include_in_report' in content,
        'business_rules': 'business_rules' in content.lower(),
        'word_generation': 'Document()' in content,
        'multiple_formats': 'employee' in content and 'item' in content,
        'json_output': 'json.dumps' in content,
        'error_handling': content.count('try:') > 2,
        'database_integration': 'sqlite3' in content,
        'professional_formatting': 'Inches' in content and 'add_heading' in content
    }
    
    feature_score = sum(1 for v in evaluation['features'].values() if v) * 5
    evaluation['score'] += feature_score
    
    if evaluation['features']['dictionary_filtering']:
        evaluation['strengths'].append('Dictionary filtering implemented')
    if evaluation['features']['business_rules']:
        evaluation['strengths'].append('Business rules processing')
    if evaluation['features']['word_generation']:
        evaluation['strengths'].append('Professional Word document generation')
    if evaluation['features']['multiple_formats']:
        evaluation['strengths'].append('Multiple report types (employee/item/both)')
    
    return evaluation

def evaluate_report_generation_bridge():
    """Evaluate Report Generation Bridge system"""
    
    print('\n🔍 EVALUATING: Report Generation Bridge')
    print('=' * 50)
    
    # Get session for testing
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_id = cursor.fetchone()[0]
    conn.close()
    
    evaluation = {
        'name': 'Report Generation Bridge',
        'file': 'core/report_generation_bridge.py',
        'strengths': [],
        'weaknesses': [],
        'features': {},
        'test_results': {},
        'score': 0
    }
    
    # Test functionality
    print('🧪 Testing functionality...')
    
    try:
        result = subprocess.run(
            [sys.executable, 'core/report_generation_bridge.py', 'generate-report', session_id],
            capture_output=True, text=True, timeout=30
        )
        if result.returncode == 0:
            output = json.loads(result.stdout)
            evaluation['test_results']['main_report'] = {
                'status': 'success',
                'files_created': len(output.get('files', {})),
                'total_changes': output.get('total_changes', 0)
            }
            evaluation['strengths'].append('Main report generation working')
            evaluation['score'] += 30
        else:
            evaluation['test_results']['main_report'] = {'status': 'failed', 'error': result.stderr}
            evaluation['weaknesses'].append('Main report generation failed')
    except Exception as e:
        evaluation['test_results']['main_report'] = {'status': 'error', 'error': str(e)}
        evaluation['weaknesses'].append(f'Main report test error: {str(e)[:50]}')
    
    # Analyze features
    with open('core/report_generation_bridge.py', 'r') as f:
        content = f.read()
    
    evaluation['features'] = {
        'dictionary_filtering': 'dictionary_items' in content and 'include_in_report' in content,
        'word_generation': 'Document()' in content,
        'pdf_generation': 'pdf' in content.lower(),
        'html_generation': 'html' in content.lower(),
        'json_output': 'json.dumps' in content,
        'error_handling': content.count('try:') > 1,
        'database_integration': 'sqlite3' in content,
        'ui_integration': 'bridge' in content.lower()
    }
    
    feature_score = sum(1 for v in evaluation['features'].values() if v) * 5
    evaluation['score'] += feature_score
    
    if evaluation['features']['dictionary_filtering']:
        evaluation['strengths'].append('Dictionary filtering implemented')
    if evaluation['features']['word_generation']:
        evaluation['strengths'].append('Word document generation')
    if evaluation['features']['pdf_generation']:
        evaluation['strengths'].append('PDF generation capability')
    if evaluation['features']['ui_integration']:
        evaluation['strengths'].append('UI integration bridge')
    
    return evaluation

def evaluate_perfect_extraction_integration():
    """Evaluate Perfect Extraction Integration system"""
    
    print('\n🔍 EVALUATING: Perfect Extraction Integration')
    print('=' * 50)
    
    evaluation = {
        'name': 'Perfect Extraction Integration',
        'file': 'core/perfect_extraction_integration.py',
        'strengths': [],
        'weaknesses': [],
        'features': {},
        'test_results': {},
        'score': 0
    }
    
    # Test functionality
    print('🧪 Testing functionality...')
    
    try:
        result = subprocess.run(
            [sys.executable, 'core/perfect_extraction_integration.py', 'generate-report'],
            capture_output=True, text=True, timeout=30
        )
        if result.returncode == 0:
            try:
                output = json.loads(result.stdout)
                evaluation['test_results']['integration_report'] = {
                    'status': 'success',
                    'output_type': type(output).__name__
                }
                evaluation['strengths'].append('Integration system working')
                evaluation['score'] += 20
            except json.JSONDecodeError:
                evaluation['test_results']['integration_report'] = {
                    'status': 'success_no_json',
                    'output_sample': result.stdout[:100]
                }
                evaluation['weaknesses'].append('No JSON output format')
        else:
            evaluation['test_results']['integration_report'] = {'status': 'failed', 'error': result.stderr}
            evaluation['weaknesses'].append('Integration system failed')
    except Exception as e:
        evaluation['test_results']['integration_report'] = {'status': 'error', 'error': str(e)}
        evaluation['weaknesses'].append(f'Integration test error: {str(e)[:50]}')
    
    # Analyze features
    with open('core/perfect_extraction_integration.py', 'r') as f:
        content = f.read()
    
    evaluation['features'] = {
        'database_integration': 'sqlite3' in content,
        'data_processing': 'comparison_results' in content,
        'session_support': 'session_id' in content,
        'error_handling': content.count('try:') > 5,
        'comprehensive_logic': len(content) > 2000,
        'json_output': 'json' in content.lower(),
        'file_generation': 'file' in content.lower() and 'path' in content.lower(),
        'extraction_logic': 'extract' in content.lower()
    }
    
    feature_score = sum(1 for v in evaluation['features'].values() if v) * 5
    evaluation['score'] += feature_score
    
    if evaluation['features']['comprehensive_logic']:
        evaluation['strengths'].append('Comprehensive system (2400+ lines)')
    if evaluation['features']['database_integration']:
        evaluation['strengths'].append('Strong database integration')
    if evaluation['features']['extraction_logic']:
        evaluation['strengths'].append('Data extraction capabilities')
    
    # Check if it actually creates files
    if 'reports/' not in content and 'save(' not in content:
        evaluation['weaknesses'].append('May not create actual report files')
        evaluation['score'] -= 10
    
    return evaluation

def main():
    """Main evaluation function"""
    
    print('🎯 DETAILED SYSTEM EVALUATION FOR CONSOLIDATION')
    print('=' * 70)
    
    # Evaluate all three working systems
    evaluations = [
        evaluate_advanced_report_generator(),
        evaluate_report_generation_bridge(),
        evaluate_perfect_extraction_integration()
    ]
    
    # Generate comparison
    print('\n📊 SYSTEM COMPARISON SUMMARY')
    print('=' * 50)
    
    for eval_data in evaluations:
        print(f'\n🏆 {eval_data["name"]}:')
        print(f'   Overall Score: {eval_data["score"]}/100')
        print(f'   Strengths: {len(eval_data["strengths"])}')
        print(f'   Weaknesses: {len(eval_data["weaknesses"])}')
        
        print('   ✅ Strengths:')
        for strength in eval_data["strengths"]:
            print(f'      • {strength}')
        
        if eval_data["weaknesses"]:
            print('   ⚠️  Weaknesses:')
            for weakness in eval_data["weaknesses"]:
                print(f'      • {weakness}')
    
    # Determine winner
    best_system = max(evaluations, key=lambda x: x['score'])
    
    print(f'\n🏆 RECOMMENDED SYSTEM TO KEEP:')
    print(f'   {best_system["name"]} (Score: {best_system["score"]}/100)')
    
    return evaluations, best_system

if __name__ == "__main__":
    evaluations, winner = main()
