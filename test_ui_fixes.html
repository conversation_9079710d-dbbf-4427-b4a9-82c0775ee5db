<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test UI Fixes - Interactive Pre-Reporting</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-header {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-expand {
            background: #28a745;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .sort-dropdown {
            padding: 6px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background: white;
            min-width: 150px;
        }
        .persistent-sort-controls {
            margin: 15px 0;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            display: block !important;
            visibility: visible !important;
        }
        .sort-control-row {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .sort-field {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .consolidated-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
            padding: 15px;
            background: white;
        }
        .consolidated-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .consolidated-details {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .consolidated-details.expanded {
            display: block;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🧪 UI Fixes Test - Interactive Pre-Reporting</h1>
        <p>Testing Sort Dropdown Visibility and Expand Details Functionality</p>
    </div>

    <div class="test-container">
        <h2>📊 Test 1: Sort Dropdown Visibility</h2>
        <div id="sort-test-results"></div>
        
        <!-- Test Sort Controls -->
        <div class="persistent-sort-controls">
            <div class="sort-control-row">
                <div class="sort-field">
                    <label for="persistent-sort-dropdown">📊 Sort by:</label>
                    <select id="persistent-sort-dropdown" class="sort-dropdown">
                        <option value="category">Category (Default)</option>
                        <option value="employees">Employees</option>
                        <option value="changeFlag">Change Flag</option>
                        <option value="priority">Priority</option>
                        <option value="bulkCategory">Bulk Category</option>
                    </select>
                </div>
            </div>
        </div>
        
        <button class="btn" onclick="testSortDropdown()">🔍 Test Sort Dropdown</button>
    </div>

    <div class="test-container">
        <h2>🔄 Test 2: Expand Details Functionality</h2>
        <div id="expand-test-results"></div>
        
        <!-- Test Consolidated Item -->
        <div class="consolidated-item" data-change-id="123">
            <div class="consolidated-header">
                <span>Test Employee (EMP001) - CONSOLIDATED (5 items)</span>
                <button class="btn-expand" onclick="toggleTestDetails('123')">
                    <i class="fas fa-chevron-down"></i>
                    Expand Details
                </button>
            </div>
            <div class="consolidated-details" id="details-123">
                <h4>Individual Changes:</h4>
                <ul>
                    <li>Basic Salary: 5,000 → 5,500 (INCREASED)</li>
                    <li>Housing Allowance: 2,000 → 2,200 (INCREASED)</li>
                    <li>Transport Allowance: 1,000 → 1,100 (INCREASED)</li>
                    <li>Medical Allowance: 500 → 550 (INCREASED)</li>
                    <li>Performance Bonus: 0 → 1,000 (NEW)</li>
                </ul>
            </div>
        </div>
        
        <button class="btn" onclick="testExpandFunctionality()">🔍 Test Expand Functionality</button>
    </div>

    <div class="test-container">
        <h2>🔧 Test 3: Event Listener Persistence</h2>
        <div id="persistence-test-results"></div>
        
        <button class="btn" onclick="testEventListenerPersistence()">🔍 Test Event Persistence</button>
        <button class="btn" onclick="simulateReRender()">🔄 Simulate Re-render</button>
    </div>

    <div class="test-container">
        <h2>📋 Test Results Summary</h2>
        <div id="summary-results"></div>
    </div>

    <script>
        let testResults = {
            sortDropdown: false,
            expandFunctionality: false,
            eventPersistence: false
        };

        let expandedState = new Set();

        function testSortDropdown() {
            const results = document.getElementById('sort-test-results');
            results.innerHTML = '<p>🔍 Testing sort dropdown...</p>';
            
            const dropdown = document.getElementById('persistent-sort-dropdown');
            
            if (!dropdown) {
                results.innerHTML += '<div class="test-fail">❌ Sort dropdown not found in DOM</div>';
                return;
            }
            
            // Test visibility
            const computedStyle = window.getComputedStyle(dropdown);
            const isVisible = computedStyle.display !== 'none' && 
                             computedStyle.visibility !== 'hidden' && 
                             computedStyle.opacity !== '0';
            
            if (isVisible) {
                results.innerHTML += '<div class="test-pass">✅ Sort dropdown is visible</div>';
            } else {
                results.innerHTML += '<div class="test-fail">❌ Sort dropdown is not visible</div>';
                results.innerHTML += `<div class="test-warning">Display: ${computedStyle.display}, Visibility: ${computedStyle.visibility}, Opacity: ${computedStyle.opacity}</div>`;
            }
            
            // Test functionality
            dropdown.addEventListener('change', function() {
                results.innerHTML += '<div class="test-pass">✅ Sort dropdown change event fired</div>';
                testResults.sortDropdown = true;
                updateSummary();
            });
            
            // Trigger change event
            dropdown.value = 'employees';
            dropdown.dispatchEvent(new Event('change'));
            
            // Test if dropdown maintains value
            setTimeout(() => {
                if (dropdown.value === 'employees') {
                    results.innerHTML += '<div class="test-pass">✅ Sort dropdown maintains selected value</div>';
                } else {
                    results.innerHTML += '<div class="test-fail">❌ Sort dropdown lost selected value</div>';
                }
            }, 100);
        }

        function toggleTestDetails(id) {
            console.log(`🔄 Toggle test details called for ID: ${id}`);
            
            const detailsElement = document.getElementById(`details-${id}`);
            const button = document.querySelector(`[data-change-id="${id}"] .btn-expand`);
            
            if (expandedState.has(id)) {
                expandedState.delete(id);
                detailsElement.classList.remove('expanded');
                button.innerHTML = '<i class="fas fa-chevron-down"></i> Expand Details';
                console.log(`🔽 Collapsed details for ${id}`);
            } else {
                expandedState.add(id);
                detailsElement.classList.add('expanded');
                button.innerHTML = '<i class="fas fa-chevron-up"></i> Collapse Details';
                console.log(`🔼 Expanded details for ${id}`);
            }
        }

        function testExpandFunctionality() {
            const results = document.getElementById('expand-test-results');
            results.innerHTML = '<p>🔍 Testing expand functionality...</p>';
            
            const button = document.querySelector('[data-change-id="123"] .btn-expand');
            const details = document.getElementById('details-123');
            
            if (!button) {
                results.innerHTML += '<div class="test-fail">❌ Expand button not found</div>';
                return;
            }
            
            if (!details) {
                results.innerHTML += '<div class="test-fail">❌ Details element not found</div>';
                return;
            }
            
            // Test initial state
            const initiallyExpanded = details.classList.contains('expanded');
            results.innerHTML += `<div class="test-pass">✅ Initial state: ${initiallyExpanded ? 'Expanded' : 'Collapsed'}</div>`;
            
            // Test click functionality
            button.click();
            
            setTimeout(() => {
                const afterClickExpanded = details.classList.contains('expanded');
                if (afterClickExpanded !== initiallyExpanded) {
                    results.innerHTML += '<div class="test-pass">✅ Expand button click changes state</div>';
                    testResults.expandFunctionality = true;
                } else {
                    results.innerHTML += '<div class="test-fail">❌ Expand button click does not change state</div>';
                }
                
                // Test second click
                button.click();
                setTimeout(() => {
                    const afterSecondClick = details.classList.contains('expanded');
                    if (afterSecondClick === initiallyExpanded) {
                        results.innerHTML += '<div class="test-pass">✅ Expand button toggles correctly</div>';
                    } else {
                        results.innerHTML += '<div class="test-fail">❌ Expand button does not toggle correctly</div>';
                    }
                    updateSummary();
                }, 100);
            }, 100);
        }

        function testEventListenerPersistence() {
            const results = document.getElementById('persistence-test-results');
            results.innerHTML = '<p>🔍 Testing event listener persistence...</p>';
            
            // Test if event listeners survive DOM manipulation
            const dropdown = document.getElementById('persistent-sort-dropdown');
            const button = document.querySelector('[data-change-id="123"] .btn-expand');
            
            let dropdownWorking = false;
            let buttonWorking = false;
            
            // Test dropdown
            const originalValue = dropdown.value;
            dropdown.value = 'priority';
            dropdown.dispatchEvent(new Event('change'));
            
            setTimeout(() => {
                if (dropdown.value === 'priority') {
                    dropdownWorking = true;
                    results.innerHTML += '<div class="test-pass">✅ Dropdown event listener persists</div>';
                } else {
                    results.innerHTML += '<div class="test-fail">❌ Dropdown event listener lost</div>';
                }
                
                // Test button
                const initialState = expandedState.has('123');
                button.click();
                
                setTimeout(() => {
                    const newState = expandedState.has('123');
                    if (newState !== initialState) {
                        buttonWorking = true;
                        results.innerHTML += '<div class="test-pass">✅ Button event listener persists</div>';
                    } else {
                        results.innerHTML += '<div class="test-fail">❌ Button event listener lost</div>';
                    }
                    
                    testResults.eventPersistence = dropdownWorking && buttonWorking;
                    updateSummary();
                }, 100);
            }, 100);
        }

        function simulateReRender() {
            const results = document.getElementById('persistence-test-results');
            results.innerHTML += '<p>🔄 Simulating re-render...</p>';
            
            // Simulate what happens during re-render in the actual app
            const container = document.querySelector('.test-container:nth-child(3)');
            const originalHTML = container.innerHTML;
            
            // Replace content (simulating re-render)
            container.innerHTML = originalHTML;
            
            results.innerHTML += '<div class="test-warning">⚠️ Content re-rendered - event listeners may be lost</div>';
            
            // Test if we need to reattach listeners
            setTimeout(() => {
                testEventListenerPersistence();
            }, 100);
        }

        function updateSummary() {
            const summary = document.getElementById('summary-results');
            const passCount = Object.values(testResults).filter(Boolean).length;
            const totalTests = Object.keys(testResults).length;
            
            let html = `<h3>📊 Test Results: ${passCount}/${totalTests} Passed</h3>`;
            
            for (const [test, passed] of Object.entries(testResults)) {
                const status = passed ? '✅ PASS' : '❌ FAIL';
                const className = passed ? 'test-pass' : 'test-fail';
                html += `<div class="${className}">${status} - ${test.replace(/([A-Z])/g, ' $1').toLowerCase()}</div>`;
            }
            
            if (passCount === totalTests) {
                html += '<div class="test-pass"><strong>🎉 All tests passed! UI fixes are working correctly.</strong></div>';
            } else {
                html += '<div class="test-fail"><strong>⚠️ Some tests failed. UI fixes need attention.</strong></div>';
            }
            
            summary.innerHTML = html;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateSummary();
            
            // Auto-run basic tests
            setTimeout(() => {
                testSortDropdown();
            }, 500);
            
            setTimeout(() => {
                testExpandFunctionality();
            }, 1000);
        });
    </script>
</body>
</html>
