#!/usr/bin/env python3
"""
Comprehensive test to verify all Dictionary Manager operations properly hit the database.
This ensures we don't have incidents where the database is not respected.
"""

import sqlite3
import os
import json

def test_dictionary_database_operations():
    """Test all critical dictionary database operations"""
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    
    if not os.path.exists(db_path):
        print('❌ Database file not found')
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print('=== COMPREHENSIVE DICTIONARY DATABASE OPERATIONS CHECK ===\n')
    
    # 1. Check database schema
    print('1. 📋 CHECKING DATABASE SCHEMA:')
    cursor.execute('PRAGMA table_info(dictionary_items)')
    columns = cursor.fetchall()
    required_columns = [
        'item_name', 'include_in_report', 'include_new', 'include_increase', 
        'include_decrease', 'include_removed', 'include_no_change'
    ]
    
    print(f'   Dictionary items table has {len(columns)} columns')
    existing_columns = [col[1] for col in columns]
    schema_ok = True
    
    for req_col in required_columns:
        if req_col in existing_columns:
            print(f'   ✅ {req_col}')
        else:
            print(f'   ❌ {req_col} - MISSING!')
            schema_ok = False
    
    # 2. Check current database state
    print('\n2. 📊 CURRENT DATABASE STATE:')
    cursor.execute('SELECT COUNT(*) FROM dictionary_items')
    total_items = cursor.fetchone()[0]
    print(f'   Total items in database: {total_items}')
    
    cursor.execute('''
        SELECT ds.section_name, COUNT(*) 
        FROM dictionary_items di 
        JOIN dictionary_sections ds ON di.section_id = ds.id 
        GROUP BY ds.section_name
    ''')
    sections = cursor.fetchall()
    for section, count in sections:
        print(f'   {section}: {count} items')
    
    # 3. Test critical operations
    print('\n3. 🔍 TESTING CRITICAL OPERATIONS:')
    
    # Test include_in_report functionality
    print('   Testing include_in_report functionality...')
    cursor.execute('SELECT COUNT(*) FROM dictionary_items WHERE include_in_report = 1')
    included_count = cursor.fetchone()[0]
    cursor.execute('SELECT COUNT(*) FROM dictionary_items WHERE include_in_report = 0')
    excluded_count = cursor.fetchone()[0]
    print(f'   ✅ Items included in reports: {included_count}')
    print(f'   ✅ Items excluded from reports: {excluded_count}')
    
    # Test change detection settings
    print('   Testing change detection settings...')
    change_columns = ['include_new', 'include_increase', 'include_decrease', 'include_removed', 'include_no_change']
    for col in change_columns:
        cursor.execute(f'SELECT COUNT(*) FROM dictionary_items WHERE {col} = 1')
        enabled_count = cursor.fetchone()[0]
        print(f'   ✅ {col}: {enabled_count} items enabled')
    
    # Check for data integrity issues
    print('   Checking for potential database sync issues...')
    cursor.execute('SELECT COUNT(*) FROM dictionary_items WHERE item_name IS NULL OR item_name = ""')
    null_names = cursor.fetchone()[0]
    if null_names > 0:
        print(f'   ❌ Found {null_names} items with null/empty names')
    else:
        print('   ✅ All items have valid names')
    
    # Check auto-learned items
    cursor.execute('SELECT COUNT(*) FROM dictionary_items WHERE auto_learned = 1')
    auto_learned = cursor.fetchone()[0]
    print(f'   ✅ Auto-learned items: {auto_learned}')
    
    # 4. Identify potential issues
    print('\n4. ⚠️  POTENTIAL ISSUES CHECK:')
    
    # Check for items hidden from UI
    cursor.execute('''
        SELECT ds.section_name, di.item_name 
        FROM dictionary_items di 
        JOIN dictionary_sections ds ON di.section_id = ds.id 
        WHERE di.include_in_report = 0 
        LIMIT 5
    ''')
    hidden_items = cursor.fetchall()
    if hidden_items:
        print('   Items hidden from UI (include_in_report = 0):')
        for section, item in hidden_items:
            print(f'   - {section}: {item}')
    else:
        print('   ✅ No items are hidden from UI')
    
    # Check for missing standard_key values
    cursor.execute('SELECT COUNT(*) FROM dictionary_items WHERE standard_key IS NULL OR standard_key = ""')
    missing_keys = cursor.fetchone()[0]
    if missing_keys > 0:
        print(f'   ⚠️  {missing_keys} items missing standard_key values')
    else:
        print('   ✅ All items have standard_key values')
    
    # 5. Test specific operations that must hit database
    print('\n5. 🧪 TESTING SPECIFIC OPERATIONS:')
    
    # Test if LEAVE ALLOWANCE is properly saved
    cursor.execute('''
        SELECT ds.section_name, di.item_name, di.include_in_report, di.auto_learned
        FROM dictionary_items di 
        JOIN dictionary_sections ds ON di.section_id = ds.id 
        WHERE di.item_name = 'LEAVE ALLOWANCE'
    ''')
    leave_allowance = cursor.fetchone()
    if leave_allowance:
        section, name, include, auto = leave_allowance
        print(f'   ✅ LEAVE ALLOWANCE found in {section}')
        print(f'   ✅ Include in report: {include} ({"visible" if include else "hidden"})')
        print(f'   ✅ Auto-learned: {auto}')
    else:
        print('   ❌ LEAVE ALLOWANCE not found in database!')
    
    conn.close()
    
    print('\n6. 📋 SUMMARY:')
    if schema_ok and total_items > 0:
        print('   ✅ Database schema is correct')
        print('   ✅ Database contains dictionary items')
        print('   ✅ All critical operations appear to be database-backed')
        print('\n✅ DICTIONARY DATABASE OPERATIONS CHECK PASSED!')
        return True
    else:
        print('   ❌ Issues found with database operations')
        print('\n❌ DICTIONARY DATABASE OPERATIONS CHECK FAILED!')
        return False

if __name__ == '__main__':
    test_dictionary_database_operations()
