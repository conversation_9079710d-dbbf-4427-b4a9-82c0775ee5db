#!/usr/bin/env python3
"""
Quick evaluation of the three working report systems
"""

import os
import subprocess
import sys
import json
import sqlite3

def main():
    print('🎯 QUICK SYSTEM EVALUATION')
    print('=' * 40)

    # Get test session
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_id = cursor.fetchone()[0]
    conn.close()

    print(f'Using session: {session_id}')

    systems = [
        ('Advanced Report Generator', 'core/advanced_report_generator.py', ['generate-employee', session_id]),
        ('Report Generation Bridge', 'core/report_generation_bridge.py', ['generate-report', session_id]),
        ('Perfect Extraction Integration', 'core/perfect_extraction_integration.py', ['generate-report'])
    ]

    results = {}

    for name, script, args in systems:
        print(f'\n🧪 Testing {name}...')
        try:
            result = subprocess.run(
                [sys.executable, script] + args,
                capture_output=True, text=True, timeout=30
            )
            
            if result.returncode == 0:
                try:
                    output = json.loads(result.stdout)
                    results[name] = {
                        'status': 'success',
                        'json_output': True,
                        'files_created': len(output.get('files', {})),
                        'has_filtering_stats': 'filtering_stats' in output,
                        'sample_output': str(output)[:200]
                    }
                    files_count = len(output.get('files', {}))
                    print(f'   ✅ Working - JSON output with {files_count} files')
                except json.JSONDecodeError:
                    results[name] = {
                        'status': 'success',
                        'json_output': False,
                        'output_sample': result.stdout[:200]
                    }
                    print(f'   ⚠️  Working - Non-JSON output')
            else:
                results[name] = {
                    'status': 'failed',
                    'error': result.stderr[:200]
                }
                print(f'   ❌ Failed: {result.stderr[:100]}')
                
        except Exception as e:
            results[name] = {
                'status': 'error',
                'error': str(e)
            }
            print(f'   💥 Error: {str(e)[:100]}')

    print('\n📊 RESULTS SUMMARY:')
    for name, result in results.items():
        status_icon = {'success': '✅', 'failed': '❌', 'error': '💥'}.get(result['status'], '❓')
        print(f'{status_icon} {name}: {result["status"]}')
        if result['status'] == 'success' and result.get('json_output'):
            files = result.get('files_created', 0)
            filtering = result.get('has_filtering_stats', False)
            print(f'   Files: {files}, Filtering: {filtering}')

    # Analyze file sizes and capabilities
    print('\n📁 FILE ANALYSIS:')
    file_info = [
        ('Advanced Report Generator', 'core/advanced_report_generator.py'),
        ('Report Generation Bridge', 'core/report_generation_bridge.py'),
        ('Perfect Extraction Integration', 'core/perfect_extraction_integration.py')
    ]
    
    for name, filepath in file_info:
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            features = {
                'dictionary_filtering': 'include_in_report' in content,
                'word_generation': 'Document()' in content,
                'business_rules': 'business_rules' in content.lower(),
                'json_output': 'json.dumps' in content,
                'error_handling': content.count('try:') > 2
            }
            
            feature_count = sum(1 for v in features.values() if v)
            
            print(f'   {name}:')
            print(f'     Size: {size} bytes ({size/1024:.1f} KB)')
            print(f'     Features: {feature_count}/5')
            print(f'     Dictionary Filtering: {features["dictionary_filtering"]}')
            print(f'     Word Generation: {features["word_generation"]}')
            print(f'     Business Rules: {features["business_rules"]}')

if __name__ == "__main__":
    main()
