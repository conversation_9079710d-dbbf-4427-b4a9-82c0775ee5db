#!/usr/bin/env python3
"""
Deep analysis of Dictionary Manager toggle logic and potential conflicts
"""

import sqlite3
import os

def analyze_dictionary_toggle_logic():
    """Analyze the dictionary toggle logic for conflicts and issues"""
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print('Database not found')
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print('=== DICTIONARY MANAGER TOGGLE LOGIC ANALYSIS ===\n')
    
    # 1. Check the current filtering logic used in reports
    print('1. 📋 CURRENT FILTERING LOGIC IN REPORTS:')
    print('   The filtering logic used in all report generation functions:')
    print('   ```sql')
    print('   WHERE (')
    print('       -- MASTER TOGGLE: Include items marked for reports')
    print('       (di.include_in_report = 1 OR di.include_in_report IS NULL)')
    print('       AND')
    print('       -- CHANGE DETECTION: Filter by change type')
    print('       (')
    print('           (cr.change_type = "NEW" AND (di.include_new = 1 OR di.include_new IS NULL)) OR')
    print('           (cr.change_type = "INCREASED" AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR')
    print('           (cr.change_type = "DECREASED" AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR')
    print('           (cr.change_type = "REMOVED" AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR')
    print('           (cr.change_type = "NO_CHANGE" AND (di.include_no_change = 1 OR di.include_no_change IS NULL))')
    print('       )')
    print('   )```')
    
    # 2. Analyze the logical relationship
    print('\n2. 🔍 LOGICAL RELATIONSHIP ANALYSIS:')
    print('   This is an AND relationship, meaning BOTH conditions must be true:')
    print('   ✅ Item must be included in reports (include_in_report = 1)')
    print('   ✅ Item must allow the specific change type (include_[change_type] = 1)')
    
    # 3. Check for potential conflicts
    print('\n3. ⚠️  POTENTIAL CONFLICT SCENARIOS:')
    
    # Scenario 1: include_in_report = 0, but change detection = 1
    cursor.execute('''
        SELECT ds.section_name, di.item_name, di.include_in_report,
               di.include_new, di.include_increase, di.include_decrease, 
               di.include_removed, di.include_no_change
        FROM dictionary_items di
        JOIN dictionary_sections ds ON di.section_id = ds.id
        WHERE di.include_in_report = 0
        AND (di.include_new = 1 OR di.include_increase = 1 OR di.include_decrease = 1 
             OR di.include_removed = 1 OR di.include_no_change = 1)
        LIMIT 5
    ''')
    
    conflicts = cursor.fetchall()
    if conflicts:
        print('   🚨 CONFLICT TYPE 1: Items excluded from reports but with change detection enabled:')
        for row in conflicts:
            section, item, include_report, new, inc, dec, rem, no_change = row
            enabled_changes = []
            if new: enabled_changes.append('NEW')
            if inc: enabled_changes.append('INCREASE')
            if dec: enabled_changes.append('DECREASE')
            if rem: enabled_changes.append('REMOVED')
            if no_change: enabled_changes.append('NO_CHANGE')
            
            print(f'      - {section}.{item}: include_in_report=0, but enabled for {enabled_changes}')
            print(f'        RESULT: Will be EXCLUDED from all reports (include_in_report takes precedence)')
    else:
        print('   ✅ No Type 1 conflicts found')
    
    # Scenario 2: include_in_report = 1, but all change detection = 0
    cursor.execute('''
        SELECT ds.section_name, di.item_name, di.include_in_report,
               di.include_new, di.include_increase, di.include_decrease, 
               di.include_removed, di.include_no_change
        FROM dictionary_items di
        JOIN dictionary_sections ds ON di.section_id = ds.id
        WHERE di.include_in_report = 1
        AND di.include_new = 0 AND di.include_increase = 0 AND di.include_decrease = 0 
        AND di.include_removed = 0 AND di.include_no_change = 0
        LIMIT 5
    ''')
    
    all_disabled = cursor.fetchall()
    if all_disabled:
        print('\n   🚨 CONFLICT TYPE 2: Items included in reports but all change detection disabled:')
        for row in all_disabled:
            section, item = row[0], row[1]
            print(f'      - {section}.{item}: include_in_report=1, but all change types disabled')
            print(f'        RESULT: Will be EXCLUDED from all reports (no change types allowed)')
    else:
        print('\n   ✅ No Type 2 conflicts found')
    
    # 4. Check default values and NULL handling
    print('\n4. 🔧 DEFAULT VALUES AND NULL HANDLING:')
    cursor.execute('''
        SELECT 
            COUNT(*) as total_items,
            SUM(CASE WHEN include_in_report IS NULL THEN 1 ELSE 0 END) as null_include_report,
            SUM(CASE WHEN include_new IS NULL THEN 1 ELSE 0 END) as null_include_new,
            SUM(CASE WHEN include_increase IS NULL THEN 1 ELSE 0 END) as null_include_increase,
            SUM(CASE WHEN include_decrease IS NULL THEN 1 ELSE 0 END) as null_include_decrease,
            SUM(CASE WHEN include_removed IS NULL THEN 1 ELSE 0 END) as null_include_removed,
            SUM(CASE WHEN include_no_change IS NULL THEN 1 ELSE 0 END) as null_include_no_change
        FROM dictionary_items
    ''')
    
    stats = cursor.fetchone()
    if stats:
        total, null_report, null_new, null_inc, null_dec, null_rem, null_no_change = stats
        
        print(f'   Total dictionary items: {total}')
        print(f'   Items with NULL include_in_report: {null_report} (treated as included)')
        print(f'   Items with NULL change detection settings:')
        print(f'      - include_new: {null_new} (treated as enabled)')
        print(f'      - include_increase: {null_inc} (treated as enabled)')
        print(f'      - include_decrease: {null_dec} (treated as enabled)')
        print(f'      - include_removed: {null_rem} (treated as enabled)')
        print(f'      - include_no_change: {null_no_change} (treated as enabled)')
    
    conn.close()

if __name__ == '__main__':
    analyze_dictionary_toggle_logic()
