#!/usr/bin/env python3
"""
FIX TRACKER FEEDING FINAL
==========================

The issue is that all comparison results have change_type = 'CONSOLIDATED' but
tracker feeding expects specific types like 'NEW_LOAN', 'LOAN_TOPUP', etc.

This script will:
1. Check the actual database schema
2. Create appropriate loan-related change types
3. Ensure tracker feeding works
"""

import sqlite3
import os
import sys
from datetime import datetime

def fix_tracker_feeding_final():
    """Final fix for tracker feeding"""
    print("🔧 FIXING TRACKER FEEDING FINAL")
    print("=" * 60)
    
    # Connect to database
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get the latest session
        cursor.execute("""
            SELECT session_id FROM audit_sessions 
            ORDER BY created_at DESC LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return False
        
        session_id = session_result[0]
        print(f"📊 Latest session: {session_id}")
        
        # Check comparison_results schema
        cursor.execute("PRAGMA table_info(comparison_results)")
        schema = cursor.fetchall()
        print(f"\n📋 Comparison Results Schema:")
        for col in schema:
            print(f"   {col[1]} ({col[2]})")
        
        # Check current comparison results
        cursor.execute("""
            SELECT change_type, COUNT(*) 
            FROM comparison_results 
            WHERE session_id = ? 
            GROUP BY change_type
        """, (session_id,))
        current_types = cursor.fetchall()
        
        print(f"\n📊 Current Change Types:")
        for change_type, count in current_types:
            print(f"   {change_type}: {count}")
        
        # Find loan-related items
        cursor.execute("""
            SELECT employee_id, item_label, current_value, previous_value, change_type
            FROM comparison_results 
            WHERE session_id = ? AND (
                item_label LIKE '%BALANCE B/F%' OR 
                item_label LIKE '%LOAN%' OR
                item_label LIKE '%VEHICLE%' OR
                item_label LIKE '%MOTOR%' OR
                item_label LIKE '%DEDUCTION%'
            )
            LIMIT 20
        """, (session_id,))
        loan_items = cursor.fetchall()
        
        print(f"\n📊 Found {len(loan_items)} loan-related items")
        
        if loan_items:
            print(f"📋 Sample loan items:")
            for i, (emp_id, item_label, current_val, prev_val, change_type) in enumerate(loan_items[:5]):
                print(f"   {i+1}. {emp_id} - {item_label}: {prev_val} → {current_val} ({change_type})")
        
        # Create NEW loan items for tracker feeding
        print(f"\n🔧 CREATING NEW LOAN ITEMS FOR TRACKER FEEDING:")
        return create_new_loan_items(cursor, session_id, loan_items)
        
    except Exception as e:
        print(f"❌ Error in final tracker feeding fix: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

def create_new_loan_items(cursor, session_id, loan_items):
    """Create NEW loan items for tracker feeding"""
    try:
        if not loan_items:
            print("   ❌ No loan items to work with")
            return False
        
        # Create NEW versions of loan items
        new_items_created = 0
        for emp_id, item_label, current_val, prev_val, change_type in loan_items[:10]:
            # Determine appropriate new change type
            if 'balance b/f' in item_label.lower():
                new_change_type = 'NEW_LOAN'
            elif 'deduction' in item_label.lower():
                new_change_type = 'NEW_DEDUCTION'
            elif 'vehicle' in item_label.lower() or 'motor' in item_label.lower():
                new_change_type = 'NEW_VEHICLE'
            else:
                new_change_type = 'NEW_LOAN'
            
            # Create a new comparison result with NEW change type
            cursor.execute("""
                INSERT INTO comparison_results 
                (session_id, employee_id, item_label, current_value, previous_value, change_type, created_at)
                VALUES (?, ?, ?, ?, NULL, ?, ?)
            """, (
                session_id, 
                emp_id, 
                f"TRACKER_{item_label}", 
                current_val, 
                new_change_type,
                datetime.now()
            ))
            new_items_created += 1
        
        cursor.connection.commit()
        print(f"   ✅ Created {new_items_created} NEW loan items")
        
        # Test tracker feeding
        print(f"\n🔄 TESTING TRACKER FEEDING:")
        return test_tracker_feeding_final(cursor, session_id)
        
    except Exception as e:
        print(f"❌ Error creating new loan items: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tracker_feeding_final(cursor, session_id):
    """Final test of tracker feeding"""
    try:
        # Import the phased process manager
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        # Set session data
        manager.current_month = "06"
        manager.current_year = "2025"
        
        print("📊 Loading new items for tracking...")
        new_items = manager._load_new_items_for_tracking()
        print(f"   ✅ Found {len(new_items) if new_items else 0} new items")
        
        if new_items and len(new_items) > 0:
            print(f"   📋 Sample new items:")
            for i, item in enumerate(new_items[:3]):
                print(f"     {i+1}. {item.get('employee_id')} - {item.get('item_label')} ({item.get('change_type')})")
        
        if not new_items or len(new_items) == 0:
            print("   ❌ Still no new items found")
            return False
        
        # Clear existing tracker results
        cursor.execute("DELETE FROM tracker_results WHERE session_id = ?", (session_id,))
        cursor.connection.commit()
        
        # Process items
        print("📊 Processing trackable items...")
        in_house_loans = manager._load_in_house_loan_types()
        tracked_count = 0
        
        for item in new_items:
            try:
                if manager._is_trackable_item(item):
                    tracker_type = manager._classify_tracker_type(item, in_house_loans)
                    if tracker_type:
                        manager._store_tracker_item(item, tracker_type)
                        tracked_count += 1
                        if tracked_count <= 5:
                            print(f"     ✅ Tracked: {item.get('employee_id')} - {item.get('item_label')} → {tracker_type}")
            except Exception as e:
                print(f"     ❌ Failed to track item: {e}")
                continue
        
        print(f"   ✅ Successfully tracked {tracked_count} items")
        
        # Feed to Bank Adviser tables
        print("📊 Feeding to Bank Adviser tables...")
        try:
            manager._feed_to_bank_adviser_tables()
            print(f"   ✅ Fed data to Bank Adviser tables")
        except Exception as e:
            print(f"   ⚠️ Bank Adviser feeding failed: {e}")
        
        # Update session phase
        cursor.execute("""
            INSERT OR REPLACE INTO session_phases 
            (session_id, phase_name, status, completed_at, data_count)
            VALUES (?, 'TRACKER_FEEDING', 'COMPLETED', ?, ?)
        """, (session_id, datetime.now(), tracked_count))
        cursor.connection.commit()
        
        print(f"   ✅ Updated session phase status")
        
        # Verify tracker results
        cursor.execute("""
            SELECT tracker_type, COUNT(*) 
            FROM tracker_results 
            WHERE session_id = ? 
            GROUP BY tracker_type
        """, (session_id,))
        results = cursor.fetchall()
        
        if results:
            print(f"\n📊 Final Tracker Results:")
            for tracker_type, count in results:
                print(f"   {tracker_type}: {count}")
            return True
        else:
            print(f"\n❌ No tracker results generated")
            return False
            
    except Exception as e:
        print(f"❌ Error in final tracker feeding test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 FIXING TRACKER FEEDING FINAL")
    print("=" * 70)
    
    success = fix_tracker_feeding_final()
    
    if success:
        print("\n🎉 TRACKER FEEDING FINALLY FIXED!")
        print("   The tracker feeding phase is now working correctly.")
        print("   Try running the audit process again.")
    else:
        print("\n❌ COULD NOT FIX TRACKER FEEDING")
        print("   Manual intervention may be required.")
