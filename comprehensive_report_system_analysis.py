#!/usr/bin/env python3
"""
Comprehensive analysis of all report generation systems to determine the best one to keep
"""

import os
import sqlite3
import subprocess
import sys
import json
import glob
from datetime import datetime
from pathlib import Path

class ReportSystemAnalyzer:
    def __init__(self):
        self.db_path = os.path.join('data', 'templar_payroll_auditor.db')
        self.report_systems = {}
        self.analysis_results = {}
        
    def identify_all_report_systems(self):
        """Identify all report generation systems in the codebase"""
        
        print('🔍 IDENTIFYING ALL REPORT GENERATION SYSTEMS')
        print('=' * 60)
        
        # Define report system patterns and locations
        report_patterns = [
            ('core/advanced_report_generator.py', 'Advanced Report Generator'),
            ('core/report_generation_bridge.py', 'Report Generation Bridge'),
            ('core/perfect_extraction_integration.py', 'Perfect Extraction Integration'),
            ('generate_final_corrected_report.py', 'Final Corrected Report'),
            ('generate_corrected_employee_report.py', 'Corrected Employee Report'),
            ('generate_real_employee_report.py', 'Real Employee Report'),
            ('generate_final_specification_compliant_report.py', 'Specification Compliant Report'),
            ('core/advanced_reporting_system.py', 'Advanced Reporting System'),
        ]
        
        for file_path, system_name in report_patterns:
            if os.path.exists(file_path):
                print(f'✅ Found: {system_name} ({file_path})')
                self.report_systems[system_name] = {
                    'file_path': file_path,
                    'exists': True,
                    'size': os.path.getsize(file_path),
                    'last_modified': datetime.fromtimestamp(os.path.getmtime(file_path))
                }
            else:
                print(f'❌ Missing: {system_name} ({file_path})')
                self.report_systems[system_name] = {
                    'file_path': file_path,
                    'exists': False
                }
        
        print(f'\n📊 Total Systems Found: {len([s for s in self.report_systems.values() if s["exists"]])}/{len(self.report_systems)}')
        return self.report_systems
    
    def analyze_system_capabilities(self, system_name, file_path):
        """Analyze the capabilities of a specific report system"""
        
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
        
        capabilities = {
            'dictionary_filtering': False,
            'business_rules': False,
            'word_generation': False,
            'pdf_generation': False,
            'excel_generation': False,
            'html_generation': False,
            'database_integration': False,
            'session_support': False,
            'error_handling': False,
            'logging': False,
            'json_output': False,
            'command_line_interface': False
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()
            
            # Check for various capabilities
            if 'dictionary' in content and ('filter' in content or 'include_in_report' in content):
                capabilities['dictionary_filtering'] = True
            
            if 'business' in content and 'rules' in content:
                capabilities['business_rules'] = True
            
            if 'docx' in content or 'document()' in content or 'word' in content:
                capabilities['word_generation'] = True
            
            if 'pdf' in content:
                capabilities['pdf_generation'] = True
            
            if 'excel' in content or 'xlsx' in content or 'workbook' in content:
                capabilities['excel_generation'] = True
            
            if 'html' in content:
                capabilities['html_generation'] = True
            
            if 'sqlite' in content or 'database' in content or 'cursor' in content:
                capabilities['database_integration'] = True
            
            if 'session_id' in content:
                capabilities['session_support'] = True
            
            if 'try:' in content and 'except' in content:
                capabilities['error_handling'] = True
            
            if 'print(' in content or 'logging' in content:
                capabilities['logging'] = True
            
            if 'json' in content:
                capabilities['json_output'] = True
            
            if 'sys.argv' in content or '__main__' in content:
                capabilities['command_line_interface'] = True
            
            return capabilities
            
        except Exception as e:
            return {'error': f'Analysis failed: {str(e)}'}
    
    def test_system_functionality(self, system_name, file_path):
        """Test if a report system is functional"""
        
        if not os.path.exists(file_path):
            return {'status': 'missing', 'error': 'File not found'}
        
        # Get test session
        if not os.path.exists(self.db_path):
            return {'status': 'no_database', 'error': 'Database not found'}
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
        session_result = cursor.fetchone()
        conn.close()
        
        if not session_result:
            return {'status': 'no_session', 'error': 'No test session available'}
        
        session_id = session_result[0]
        
        # Test different command patterns
        test_commands = [
            [sys.executable, file_path, session_id],
            [sys.executable, file_path, 'generate-report', session_id],
            [sys.executable, file_path, 'generate-employee', session_id],
            [sys.executable, file_path, '--help'],
        ]
        
        for cmd in test_commands:
            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    output = result.stdout
                    if 'success' in output.lower() or 'generated' in output.lower() or '{' in output:
                        return {
                            'status': 'working',
                            'command': ' '.join(cmd),
                            'output_sample': output[:200]
                        }
                
            except subprocess.TimeoutExpired:
                return {'status': 'timeout', 'command': ' '.join(cmd)}
            except Exception as e:
                continue
        
        return {'status': 'not_working', 'error': 'No working command found'}
    
    def analyze_database_usage(self):
        """Analyze which systems are actually being used based on database records"""
        
        if not os.path.exists(self.db_path):
            return {}
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get report categories and their usage
        cursor.execute('''
            SELECT report_category, COUNT(*) as count, MAX(created_at) as last_used
            FROM reports 
            GROUP BY report_category 
            ORDER BY count DESC
        ''')
        
        usage_stats = {}
        for category, count, last_used in cursor.fetchall():
            usage_stats[category] = {
                'count': count,
                'last_used': last_used
            }
        
        conn.close()
        return usage_stats
    
    def evaluate_code_quality(self, file_path):
        """Evaluate code quality metrics"""
        
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            metrics = {
                'total_lines': len(lines),
                'code_lines': len([l for l in lines if l.strip() and not l.strip().startswith('#')]),
                'comment_lines': len([l for l in lines if l.strip().startswith('#')]),
                'docstring_lines': content.count('"""') + content.count("'''"),
                'function_count': content.count('def '),
                'class_count': content.count('class '),
                'import_count': content.count('import '),
                'error_handling_blocks': content.count('try:'),
                'documentation_ratio': 0
            }
            
            if metrics['code_lines'] > 0:
                metrics['documentation_ratio'] = (metrics['comment_lines'] + metrics['docstring_lines']) / metrics['code_lines']
            
            return metrics
            
        except Exception as e:
            return {'error': f'Quality analysis failed: {str(e)}'}
    
    def run_comprehensive_analysis(self):
        """Run comprehensive analysis of all report systems"""
        
        print('🎯 COMPREHENSIVE REPORT SYSTEM ANALYSIS')
        print('=' * 70)
        
        # Step 1: Identify all systems
        self.identify_all_report_systems()
        
        # Step 2: Analyze database usage
        print('\n📊 DATABASE USAGE ANALYSIS')
        print('-' * 40)
        usage_stats = self.analyze_database_usage()
        for category, stats in usage_stats.items():
            print(f'   • {category}: {stats["count"]} reports (last: {stats["last_used"]})')
        
        # Step 3: Analyze each system
        print('\n🔍 DETAILED SYSTEM ANALYSIS')
        print('-' * 40)
        
        for system_name, system_info in self.report_systems.items():
            if not system_info['exists']:
                continue
                
            print(f'\n📋 Analyzing: {system_name}')
            file_path = system_info['file_path']
            
            # Capabilities analysis
            capabilities = self.analyze_system_capabilities(system_name, file_path)
            
            # Functionality test
            functionality = self.test_system_functionality(system_name, file_path)
            
            # Code quality
            quality = self.evaluate_code_quality(file_path)
            
            # Store results
            self.analysis_results[system_name] = {
                'info': system_info,
                'capabilities': capabilities,
                'functionality': functionality,
                'quality': quality
            }
            
            # Display summary
            if 'error' not in capabilities:
                cap_count = sum(1 for v in capabilities.values() if v)
                print(f'   Capabilities: {cap_count}/12 features')
            
            print(f'   Functionality: {functionality["status"]}')
            
            if 'error' not in quality:
                print(f'   Code Quality: {quality["total_lines"]} lines, {quality["function_count"]} functions')
        
        return self.analysis_results

def main():
    """Main analysis function"""
    analyzer = ReportSystemAnalyzer()
    results = analyzer.run_comprehensive_analysis()
    
    # Generate summary and recommendations
    print('\n🎯 ANALYSIS SUMMARY AND RECOMMENDATIONS')
    print('=' * 60)
    
    working_systems = []
    for system_name, analysis in results.items():
        if analysis['functionality']['status'] == 'working':
            working_systems.append((system_name, analysis))
    
    print(f'Working Systems: {len(working_systems)}/{len(results)}')
    
    for system_name, analysis in working_systems:
        caps = analysis['capabilities']
        quality = analysis['quality']
        
        if 'error' not in caps and 'error' not in quality:
            cap_score = sum(1 for v in caps.values() if v)
            quality_score = min(quality.get('documentation_ratio', 0) * 100, 100)
            
            print(f'\n✅ {system_name}:')
            print(f'   Capability Score: {cap_score}/12')
            print(f'   Quality Score: {quality_score:.1f}%')
            print(f'   Status: {analysis["functionality"]["status"]}')

if __name__ == "__main__":
    main()
