#!/usr/bin/env python3
"""
Test to identify missing database operations in Dictionary Manager.
This checks for operations that might only update the in-memory dictionary
without persisting to the database.
"""

import sqlite3
import os
import json

def test_missing_database_operations():
    """Test for missing database operations"""
    
    print('=== TESTING FOR MISSING DATABASE OPERATIONS ===\n')
    
    # Check if the Dictionary Manager Python class has proper database integration
    print('1. 🔍 CHECKING DICTIONARY MANAGER DATABASE INTEGRATION:')
    
    try:
        # Import and test the dictionary manager
        import sys
        sys.path.append('core')
        from dictionary_manager import PayrollDictionaryManager

        # Test if it initializes with database
        dm = PayrollDictionaryManager(use_database=True, debug=True)
        print('   ✅ PayrollDictionaryManager initializes with database')

        # Test if it has database connection
        if hasattr(dm, 'database') and dm.database:
            print('   ✅ Database connection established')
        else:
            print('   ❌ No database connection found')
            return False

        # Test critical methods
        critical_methods = [
            'load_dictionary', 'save_dictionary', 'add_item', 'remove_item',
            'set_include_in_report', 'set_change_type_inclusion'
        ]

        for method in critical_methods:
            if hasattr(dm, method):
                print(f'   ✅ {method} method exists')
            else:
                print(f'   ❌ {method} method missing')

    except Exception as e:
        print(f'   ❌ Error testing PayrollDictionaryManager: {e}')
        return False
    
    # Check database operations in the UI handlers
    print('\n2. 🔍 CHECKING UI OPERATION DATABASE PERSISTENCE:')
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print('   ❌ Database file not found')
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Test 1: Check if delete operations are properly handled
    print('   Testing delete operation database consistency...')
    
    # Get current count
    cursor.execute('SELECT COUNT(*) FROM dictionary_items')
    current_count = cursor.fetchone()[0]
    print(f'   Current items in database: {current_count}')
    
    # Test 2: Check if toggle operations persist
    print('   Testing toggle operation persistence...')
    
    # Find an item to test toggle on
    cursor.execute('''
        SELECT di.id, ds.section_name, di.item_name, di.include_in_report
        FROM dictionary_items di 
        JOIN dictionary_sections ds ON di.section_id = ds.id 
        LIMIT 1
    ''')
    test_item = cursor.fetchone()
    
    if test_item:
        item_id, section, name, current_include = test_item
        print(f'   Test item: {section}.{name} (include_in_report: {current_include})')
        
        # Simulate toggle operation (flip the value)
        new_value = 1 if current_include == 0 else 0
        cursor.execute(
            'UPDATE dictionary_items SET include_in_report = ? WHERE id = ?',
            (new_value, item_id)
        )
        conn.commit()
        
        # Verify the change persisted
        cursor.execute('SELECT include_in_report FROM dictionary_items WHERE id = ?', (item_id,))
        updated_value = cursor.fetchone()[0]
        
        if updated_value == new_value:
            print('   ✅ Toggle operation persists to database')
            
            # Restore original value
            cursor.execute(
                'UPDATE dictionary_items SET include_in_report = ? WHERE id = ?',
                (current_include, item_id)
            )
            conn.commit()
        else:
            print('   ❌ Toggle operation does not persist to database')
    
    # Test 3: Check if add/edit operations work
    print('   Testing add/edit operation database integration...')
    
    # Try to add a test item
    test_section = 'EARNINGS'
    test_item_name = 'TEST_DATABASE_OPERATION'
    
    # Get section ID
    cursor.execute('SELECT id FROM dictionary_sections WHERE section_name = ?', (test_section,))
    section_result = cursor.fetchone()
    
    if section_result:
        section_id = section_result[0]
        
        # Add test item
        cursor.execute('''
            INSERT INTO dictionary_items 
            (section_id, item_name, standard_key, include_in_report, include_new, 
             include_increase, include_decrease, include_removed, include_no_change)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (section_id, test_item_name, 'test_database_operation', 1, 1, 1, 1, 1, 0))
        conn.commit()
        
        # Verify it was added
        cursor.execute(
            'SELECT COUNT(*) FROM dictionary_items WHERE item_name = ?',
            (test_item_name,)
        )
        if cursor.fetchone()[0] > 0:
            print('   ✅ Add operation works with database')
            
            # Clean up - remove test item
            cursor.execute('DELETE FROM dictionary_items WHERE item_name = ?', (test_item_name,))
            conn.commit()
        else:
            print('   ❌ Add operation failed to persist to database')
    
    # Test 4: Check for orphaned operations (operations that don't hit database)
    print('\n3. 🔍 CHECKING FOR POTENTIAL ORPHANED OPERATIONS:')
    
    # Check if there are any items in memory that aren't in database
    # This would indicate operations that don't persist
    
    # Load dictionary from Python manager
    try:
        dm = PayrollDictionaryManager(use_database=True, debug=False)
        dm.load_dictionary()
        memory_dict = dm.get_dictionary()

        # Count items in memory
        memory_count = 0
        for section_name, section_data in memory_dict.items():
            if isinstance(section_data, dict) and 'items' in section_data:
                memory_count += len(section_data['items'])

        print(f'   Items in memory dictionary: {memory_count}')
        print(f'   Items in database: {current_count}')

        if memory_count == current_count:
            print('   ✅ Memory and database are in sync')
        else:
            print(f'   ⚠️  Memory ({memory_count}) and database ({current_count}) counts differ')
            print('   This could indicate operations that don\'t persist to database')

    except Exception as e:
        print(f'   ❌ Error comparing memory vs database: {e}')
    
    conn.close()
    
    print('\n4. 📋 CRITICAL OPERATIONS THAT MUST HIT DATABASE:')
    operations_to_check = [
        'Toggle include_in_report',
        'Toggle change detection settings',
        'Add new dictionary item',
        'Edit existing dictionary item', 
        'Delete dictionary item',
        'Save dictionary',
        'Auto-learning approval'
    ]
    
    for op in operations_to_check:
        print(f'   📌 {op}')
    
    print('\n✅ MISSING DATABASE OPERATIONS CHECK COMPLETED!')
    return True

if __name__ == '__main__':
    test_missing_database_operations()
