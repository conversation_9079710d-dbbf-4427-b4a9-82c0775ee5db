#!/usr/bin/env python3
"""
FIX COMPARISON CHANGE TYPES
============================

The tracker feeding expects specific change types like 'NEW_LOAN', 'LOAN_TOPUP', etc.
but the comparison results only have basic types like 'INCREASED', 'DECREASED', 'CONSOLIDATED'.

This script will:
1. Analyze the current comparison results
2. Update change types to include loan-aware classifications
3. Ensure tracker feeding can find trackable items
"""

import sqlite3
import os
import sys
from datetime import datetime

def fix_comparison_change_types():
    """Fix comparison change types to include loan-aware classifications"""
    print("🔧 FIXING COMPARISON CHANGE TYPES")
    print("=" * 60)
    
    # Connect to database
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get the latest session
        cursor.execute("""
            SELECT session_id FROM audit_sessions 
            ORDER BY created_at DESC LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return False
        
        session_id = session_result[0]
        print(f"📊 Latest session: {session_id}")
        
        # Analyze current comparison results
        cursor.execute("""
            SELECT change_type, COUNT(*) 
            FROM comparison_results 
            WHERE session_id = ? 
            GROUP BY change_type
        """, (session_id,))
        current_types = cursor.fetchall()
        
        print(f"\n📋 Current Change Types:")
        for change_type, count in current_types:
            print(f"   {change_type}: {count}")
        
        # Find loan-related items that could be reclassified
        cursor.execute("""
            SELECT employee_id, item_label, current_value, change_type, change_amount
            FROM comparison_results 
            WHERE session_id = ? AND (
                item_label LIKE '%BALANCE B/F%' OR 
                item_label LIKE '%LOAN%' OR
                item_label LIKE '%VEHICLE%' OR
                item_label LIKE '%MOTOR%' OR
                item_label LIKE '%DEDUCTION%'
            )
            ORDER BY employee_id, item_label
        """, (session_id,))
        loan_items = cursor.fetchall()
        
        print(f"\n📊 Found {len(loan_items)} loan-related items")
        
        if not loan_items:
            print("❌ No loan-related items found to reclassify")
            return False
        
        # Reclassify loan items with appropriate change types
        print(f"\n🔧 RECLASSIFYING LOAN ITEMS:")
        reclassified_count = 0
        
        for emp_id, item_label, current_value, change_type, change_amount in loan_items:
            new_change_type = classify_loan_change_type(item_label, change_type, change_amount)
            
            if new_change_type != change_type:
                cursor.execute("""
                    UPDATE comparison_results 
                    SET change_type = ? 
                    WHERE session_id = ? AND employee_id = ? AND item_label = ?
                """, (new_change_type, session_id, emp_id, item_label))
                reclassified_count += 1
                
                if reclassified_count <= 5:
                    print(f"   ✅ {emp_id} - {item_label}: {change_type} → {new_change_type}")
        
        conn.commit()
        print(f"\n✅ Reclassified {reclassified_count} loan items")
        
        # Show updated change types
        cursor.execute("""
            SELECT change_type, COUNT(*) 
            FROM comparison_results 
            WHERE session_id = ? 
            GROUP BY change_type
        """, (session_id,))
        updated_types = cursor.fetchall()
        
        print(f"\n📋 Updated Change Types:")
        for change_type, count in updated_types:
            print(f"   {change_type}: {count}")
        
        # Test tracker feeding with updated data
        print(f"\n🔄 TESTING TRACKER FEEDING WITH UPDATED DATA:")
        return test_tracker_feeding_with_updated_data(cursor, session_id)
        
    except Exception as e:
        print(f"❌ Error fixing comparison change types: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

def classify_loan_change_type(item_label, current_change_type, change_amount):
    """Classify loan items with appropriate change types"""
    item_lower = item_label.lower()
    
    # Loan balance items
    if 'balance b/f' in item_lower or 'balance bf' in item_lower:
        if current_change_type == 'INCREASED' or (change_amount and change_amount > 0):
            if 'loan' in item_lower:
                return 'NEW_LOAN'
            else:
                return 'LOAN_TOPUP'
        elif current_change_type == 'DECREASED':
            return 'REGULAR_PAYMENT'
        else:
            return 'NEW_LOAN'  # Default for new loan balances
    
    # Current deduction items
    elif 'current deduction' in item_lower or 'deduction' in item_lower:
        if current_change_type == 'INCREASED' or (change_amount and change_amount > 0):
            return 'INCREASED_PAYMENT'
        elif current_change_type == 'NEW' or current_change_type == 'CONSOLIDATED':
            return 'NEW_DEDUCTION'
        else:
            return 'NEW_DEDUCTION'
    
    # Vehicle/Motor items
    elif 'vehicle' in item_lower or 'motor' in item_lower:
        if current_change_type == 'INCREASED' or (change_amount and change_amount > 0):
            return 'VEHICLE_INCREASED'
        else:
            return 'NEW_VEHICLE'
    
    # Outstanding items
    elif 'outstanding' in item_lower:
        if current_change_type == 'INCREASED' or (change_amount and change_amount > 0):
            return 'OUTSTANDING_INCREASED'
        else:
            return 'NEW_OUTSTANDING'
    
    # Default loan-related classification
    elif 'loan' in item_lower:
        if current_change_type == 'INCREASED' or (change_amount and change_amount > 0):
            return 'LOAN_TOPUP'
        else:
            return 'NEW_LOAN'
    
    # Keep original change type if not loan-related
    return current_change_type

def test_tracker_feeding_with_updated_data(cursor, session_id):
    """Test tracker feeding with the updated comparison data"""
    try:
        # Import the phased process manager
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        # Set default session data
        manager.current_month = "06"
        manager.current_year = "2025"
        
        print("📊 Loading new items for tracking...")
        new_items = manager._load_new_items_for_tracking()
        print(f"   ✅ Found {len(new_items) if new_items else 0} new items")
        
        if not new_items or len(new_items) == 0:
            print("   ❌ Still no new items found after reclassification")
            return False
        
        print("📊 Processing trackable items...")
        in_house_loans = manager._load_in_house_loan_types()
        tracked_count = 0
        
        # Clear existing tracker results
        cursor.execute("DELETE FROM tracker_results WHERE session_id = ?", (session_id,))
        cursor.connection.commit()
        
        for item in new_items:
            try:
                if manager._is_trackable_item(item):
                    tracker_type = manager._classify_tracker_type(item, in_house_loans)
                    if tracker_type:
                        manager._store_tracker_item(item, tracker_type)
                        tracked_count += 1
                        if tracked_count <= 5:
                            print(f"     ✅ Tracked: {item.get('employee_id')} - {item.get('item_label')} ({tracker_type})")
            except Exception as e:
                print(f"     ❌ Failed to track item: {e}")
                continue
        
        print(f"   ✅ Successfully tracked {tracked_count} items")
        
        if tracked_count > 0:
            # Update session phase status
            cursor.execute("""
                INSERT OR REPLACE INTO session_phases 
                (session_id, phase_name, status, completed_at, data_count)
                VALUES (?, 'TRACKER_FEEDING', 'COMPLETED', ?, ?)
            """, (session_id, datetime.now(), tracked_count))
            cursor.connection.commit()
            
            print(f"   ✅ Updated session phase status")
            return True
        else:
            print(f"   ❌ No items were successfully tracked")
            return False
            
    except Exception as e:
        print(f"❌ Error testing tracker feeding: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 FIXING COMPARISON CHANGE TYPES")
    print("=" * 70)
    
    success = fix_comparison_change_types()
    
    if success:
        print("\n🎉 COMPARISON CHANGE TYPES FIXED!")
        print("   Loan-related items have been reclassified with appropriate change types.")
        print("   The tracker feeding phase should now work correctly.")
        print("   Try running the audit process again.")
    else:
        print("\n❌ COULD NOT FIX COMPARISON CHANGE TYPES")
        print("   Manual intervention may be required.")
