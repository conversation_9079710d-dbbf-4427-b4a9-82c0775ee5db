#!/usr/bin/env python3
"""
Centralized Dictionary Filter Manager for Production
Provides consistent filtering logic and comprehensive statistics
"""

import sqlite3
import os
from datetime import datetime
import json

class DictionaryFilterManager:
    """Centralized manager for dictionary filtering operations"""
    
    def __init__(self, db_path='data/templar_payroll_auditor.db'):
        self.db_path = db_path
        
    def get_filtering_statistics(self, session_id):
        """Get comprehensive filtering statistics for a session"""
        
        if not os.path.exists(self.db_path):
            return {"error": "Database not found"}
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Get total unfiltered count
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
            total_results = cursor.fetchone()[0]
            
            # Get filtered count using standard filtering logic
            cursor.execute('''
                SELECT COUNT(*)
                FROM comparison_results cr
                LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
                LEFT JOIN dictionary_sections ds ON di.section_id = ds.id AND cr.section_name = ds.section_name
                WHERE cr.session_id = ?
                AND (
                    -- Include items that are marked to be included in reports
                    (di.include_in_report = 1 OR di.include_in_report IS NULL)
                    AND
                    -- Apply change detection filtering based on change type
                    (
                        (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                        (cr.change_type = 'INCREASED' AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                        (cr.change_type = 'DECREASED' AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                        (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                        (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1 OR di.include_no_change IS NULL)) OR
                        (cr.change_type NOT IN ('NEW', 'INCREASED', 'DECREASED', 'REMOVED', 'NO_CHANGE'))
                    )
                )
            ''', (session_id,))
            
            filtered_results = cursor.fetchone()[0]
            
            # Get breakdown by change type (filtered)
            cursor.execute('''
                SELECT cr.change_type, COUNT(*) as count
                FROM comparison_results cr
                LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
                LEFT JOIN dictionary_sections ds ON di.section_id = ds.id AND cr.section_name = ds.section_name
                WHERE cr.session_id = ?
                AND (
                    (di.include_in_report = 1 OR di.include_in_report IS NULL)
                    AND
                    (
                        (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                        (cr.change_type = 'INCREASED' AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                        (cr.change_type = 'DECREASED' AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                        (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                        (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1 OR di.include_no_change IS NULL)) OR
                        (cr.change_type NOT IN ('NEW', 'INCREASED', 'DECREASED', 'REMOVED', 'NO_CHANGE'))
                    )
                )
                GROUP BY cr.change_type
                ORDER BY count DESC
            ''', (session_id,))
            
            change_type_breakdown = dict(cursor.fetchall())
            
            # Get excluded items summary
            cursor.execute('''
                SELECT 
                    CASE 
                        WHEN di.include_in_report = 0 THEN 'Excluded by include_in_report'
                        WHEN cr.change_type = 'NEW' AND di.include_new = 0 THEN 'Excluded by include_new'
                        WHEN cr.change_type = 'INCREASED' AND di.include_increase = 0 THEN 'Excluded by include_increase'
                        WHEN cr.change_type = 'DECREASED' AND di.include_decrease = 0 THEN 'Excluded by include_decrease'
                        WHEN cr.change_type = 'REMOVED' AND di.include_removed = 0 THEN 'Excluded by include_removed'
                        WHEN cr.change_type = 'NO_CHANGE' AND di.include_no_change = 0 THEN 'Excluded by include_no_change'
                        ELSE 'Other exclusion'
                    END as exclusion_reason,
                    COUNT(*) as count
                FROM comparison_results cr
                LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
                LEFT JOIN dictionary_sections ds ON di.section_id = ds.id AND cr.section_name = ds.section_name
                WHERE cr.session_id = ?
                AND NOT (
                    (di.include_in_report = 1 OR di.include_in_report IS NULL)
                    AND
                    (
                        (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                        (cr.change_type = 'INCREASED' AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                        (cr.change_type = 'DECREASED' AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                        (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                        (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1 OR di.include_no_change IS NULL)) OR
                        (cr.change_type NOT IN ('NEW', 'INCREASED', 'DECREASED', 'REMOVED', 'NO_CHANGE'))
                    )
                )
                GROUP BY exclusion_reason
                ORDER BY count DESC
            ''', (session_id,))
            
            exclusion_breakdown = dict(cursor.fetchall())
            
            # Calculate statistics
            excluded_results = total_results - filtered_results
            reduction_percentage = (excluded_results / total_results * 100) if total_results > 0 else 0
            
            statistics = {
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "total_results": total_results,
                "filtered_results": filtered_results,
                "excluded_results": excluded_results,
                "reduction_percentage": round(reduction_percentage, 1),
                "change_type_breakdown": change_type_breakdown,
                "exclusion_breakdown": exclusion_breakdown,
                "filtering_effectiveness": "High" if reduction_percentage > 30 else "Medium" if reduction_percentage > 10 else "Low"
            }
            
            return statistics
            
        except Exception as e:
            return {"error": f"Failed to get filtering statistics: {str(e)}"}
        finally:
            conn.close()
    
    def apply_standard_filtering(self, session_id):
        """Apply standard dictionary filtering and return results with statistics"""
        
        if not os.path.exists(self.db_path):
            return None, {"error": "Database not found"}
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Apply standard filtering query
            cursor.execute('''
                SELECT cr.employee_id, cr.employee_name, cr.section_name, cr.item_label,
                       cr.previous_value, cr.current_value, cr.change_type, cr.priority
                FROM comparison_results cr
                LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
                LEFT JOIN dictionary_sections ds ON di.section_id = ds.id AND cr.section_name = ds.section_name
                WHERE cr.session_id = ?
                AND (
                    -- Include items that are marked to be included in reports
                    (di.include_in_report = 1 OR di.include_in_report IS NULL)
                    AND
                    -- Apply change detection filtering based on change type
                    (
                        (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                        (cr.change_type = 'INCREASED' AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                        (cr.change_type = 'DECREASED' AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                        (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                        (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1 OR di.include_no_change IS NULL)) OR
                        (cr.change_type NOT IN ('NEW', 'INCREASED', 'DECREASED', 'REMOVED', 'NO_CHANGE'))
                    )
                )
                ORDER BY cr.employee_name, cr.section_name, cr.item_label
            ''', (session_id,))
            
            filtered_data = cursor.fetchall()
            
            # Get statistics
            statistics = self.get_filtering_statistics(session_id)
            
            return filtered_data, statistics
            
        except Exception as e:
            return None, {"error": f"Failed to apply filtering: {str(e)}"}
        finally:
            conn.close()
    
    def validate_toggle_configuration(self, section_name, item_name):
        """Validate toggle configuration for conflicts and issues"""
        
        if not os.path.exists(self.db_path):
            return {"error": "Database not found"}
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Get current toggle configuration
            cursor.execute('''
                SELECT di.include_in_report, di.include_new, di.include_increase, 
                       di.include_decrease, di.include_removed, di.include_no_change
                FROM dictionary_items di
                JOIN dictionary_sections ds ON di.section_id = ds.id
                WHERE ds.section_name = ? AND di.item_name = ?
            ''', (section_name, item_name))
            
            result = cursor.fetchone()
            if not result:
                return {"warnings": [], "errors": [], "info": "Item not found in dictionary"}
            
            include_report, include_new, include_inc, include_dec, include_rem, include_no_change = result
            
            warnings = []
            errors = []
            
            # Check for Type 1 conflicts: include_in_report=0 but change detection enabled
            if include_report == 0:
                enabled_changes = []
                if include_new == 1: enabled_changes.append('NEW')
                if include_inc == 1: enabled_changes.append('INCREASED')
                if include_dec == 1: enabled_changes.append('DECREASED')
                if include_rem == 1: enabled_changes.append('REMOVED')
                if include_no_change == 1: enabled_changes.append('NO_CHANGE')
                
                if enabled_changes:
                    warnings.append(f"Item excluded from reports but has enabled change detection: {enabled_changes}. The item will be excluded from all reports regardless of change detection settings.")
            
            # Check for Type 2 conflicts: include_in_report=1 but all change detection disabled
            if include_report == 1:
                all_disabled = (include_new == 0 and include_inc == 0 and include_dec == 0 and 
                               include_rem == 0 and include_no_change == 0)
                if all_disabled:
                    warnings.append("Item included in reports but all change detection disabled. The item will be excluded from all reports because no change types are allowed.")
            
            return {
                "warnings": warnings,
                "errors": errors,
                "configuration": {
                    "include_in_report": include_report,
                    "include_new": include_new,
                    "include_increase": include_inc,
                    "include_decrease": include_dec,
                    "include_removed": include_rem,
                    "include_no_change": include_no_change
                }
            }
            
        except Exception as e:
            return {"error": f"Failed to validate configuration: {str(e)}"}
        finally:
            conn.close()

def main():
    """Command line interface for testing"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python dictionary_filter_manager.py <command> [args]")
        print("Commands:")
        print("  stats <session_id>     - Get filtering statistics")
        print("  validate <section> <item> - Validate toggle configuration")
        return
    
    manager = DictionaryFilterManager()
    command = sys.argv[1]
    
    if command == "stats" and len(sys.argv) > 2:
        session_id = sys.argv[2]
        stats = manager.get_filtering_statistics(session_id)
        print(json.dumps(stats, indent=2))
    
    elif command == "validate" and len(sys.argv) > 3:
        section = sys.argv[2]
        item = sys.argv[3]
        validation = manager.validate_toggle_configuration(section, item)
        print(json.dumps(validation, indent=2))
    
    else:
        print("Invalid command or missing arguments")

if __name__ == "__main__":
    main()
