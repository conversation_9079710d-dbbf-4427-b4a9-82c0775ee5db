#!/usr/bin/env python3
"""
Comprehensive validation of the consolidated report system
"""

import os
import subprocess
import sys
import json
import sqlite3
from datetime import datetime

def test_consolidated_system():
    """Test all aspects of the consolidated reporting system"""
    
    print('🎯 CONSOLIDATED SYSTEM VALIDATION')
    print('=' * 50)
    
    # Get test session
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print('❌ Database not found')
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    conn.close()
    
    if not session_result:
        print('❌ No test session available')
        return False
    
    session_id = session_result[0]
    print(f'✅ Using test session: {session_id}')
    
    # Test results
    test_results = {
        'employee_report': False,
        'item_report': False,
        'both_reports': False,
        'main_report': False,
        'all_formats': False,
        'dictionary_filtering': False,
        'business_rules': False,
        'file_generation': False
    }
    
    # Test 1: Employee-based report
    print('\n🧪 Testing Employee-Based Report...')
    try:
        result = subprocess.run(
            [sys.executable, 'core/advanced_report_generator.py', 'generate-employee', session_id],
            capture_output=True, text=True, timeout=30
        )
        if result.returncode == 0:
            output = json.loads(result.stdout)
            if output.get('success') and 'employee_word' in output.get('files', {}):
                print('   ✅ Employee report generation: PASSED')
                test_results['employee_report'] = True
                
                # Check file exists
                file_path = output['files']['employee_word']
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f'   ✅ File created: {file_path} ({file_size} bytes)')
                    test_results['file_generation'] = True
                
                # Check dictionary filtering
                if output.get('filtering_stats', {}).get('reduction_percentage', 0) > 0:
                    print(f'   ✅ Dictionary filtering: {output["filtering_stats"]["reduction_percentage"]}% reduction')
                    test_results['dictionary_filtering'] = True
                
                # Check business rules
                if output.get('processed_changes', 0) > 0:
                    print(f'   ✅ Business rules: {output["processed_changes"]} changes processed')
                    test_results['business_rules'] = True
            else:
                print('   ❌ Employee report generation: FAILED')
        else:
            print(f'   ❌ Employee report generation: FAILED ({result.stderr})')
    except Exception as e:
        print(f'   ❌ Employee report generation: ERROR ({str(e)})')
    
    # Test 2: Item-based report
    print('\n🧪 Testing Item-Based Report...')
    try:
        result = subprocess.run(
            [sys.executable, 'core/advanced_report_generator.py', 'generate-item', session_id],
            capture_output=True, text=True, timeout=30
        )
        if result.returncode == 0:
            output = json.loads(result.stdout)
            if output.get('success') and 'item_word' in output.get('files', {}):
                print('   ✅ Item report generation: PASSED')
                test_results['item_report'] = True
            else:
                print('   ❌ Item report generation: FAILED')
        else:
            print(f'   ❌ Item report generation: FAILED ({result.stderr})')
    except Exception as e:
        print(f'   ❌ Item report generation: ERROR ({str(e)})')
    
    # Test 3: Both reports
    print('\n🧪 Testing Both Report Types...')
    try:
        result = subprocess.run(
            [sys.executable, 'core/advanced_report_generator.py', 'generate-both', session_id],
            capture_output=True, text=True, timeout=30
        )
        if result.returncode == 0:
            output = json.loads(result.stdout)
            if (output.get('success') and 
                'employee_word' in output.get('files', {}) and 
                'item_word' in output.get('files', {})):
                print('   ✅ Both reports generation: PASSED')
                test_results['both_reports'] = True
            else:
                print('   ❌ Both reports generation: FAILED')
        else:
            print(f'   ❌ Both reports generation: FAILED ({result.stderr})')
    except Exception as e:
        print(f'   ❌ Both reports generation: ERROR ({str(e)})')
    
    # Test 4: Main report (replaces Report Generation Bridge)
    print('\n🧪 Testing Main Report Generation...')
    try:
        result = subprocess.run(
            [sys.executable, 'core/advanced_report_generator.py', 'generate-report', session_id],
            capture_output=True, text=True, timeout=30
        )
        if result.returncode == 0:
            # Handle case where output might be empty or non-JSON
            stdout = result.stdout.strip()
            if stdout:
                try:
                    output = json.loads(stdout)
                    if (output.get('success') and
                        len(output.get('files', {})) >= 2):  # Should have Word and PDF
                        print('   ✅ Main report generation: PASSED')
                        print(f'   ✅ Generated {len(output["files"])} files: {list(output["files"].keys())}')
                        test_results['main_report'] = True
                    else:
                        print('   ❌ Main report generation: FAILED (invalid response)')
                except json.JSONDecodeError:
                    print(f'   ❌ Main report generation: FAILED (non-JSON output: {stdout[:100]})')
            else:
                print('   ❌ Main report generation: FAILED (no output)')
        else:
            print(f'   ❌ Main report generation: FAILED (exit code {result.returncode})')
            if result.stderr:
                print(f'      Error: {result.stderr[:200]}')
    except Exception as e:
        print(f'   ❌ Main report generation: ERROR ({str(e)})')
    
    # Test 5: All formats
    print('\n🧪 Testing All Formats Generation...')
    try:
        result = subprocess.run(
            [sys.executable, 'core/advanced_report_generator.py', 'generate-all-formats', session_id],
            capture_output=True, text=True, timeout=30
        )
        if result.returncode == 0:
            output = json.loads(result.stdout)
            if (output.get('success') and 
                len(output.get('files', {})) >= 3):  # Should have Word, PDF, HTML
                print('   ✅ All formats generation: PASSED')
                print(f'   ✅ Generated {len(output["files"])} files: {list(output["files"].keys())}')
                test_results['all_formats'] = True
            else:
                print('   ❌ All formats generation: FAILED')
        else:
            print(f'   ❌ All formats generation: FAILED ({result.stderr})')
    except Exception as e:
        print(f'   ❌ All formats generation: ERROR ({str(e)})')
    
    # Summary
    print('\n📊 VALIDATION SUMMARY')
    print('=' * 30)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = '✅ PASSED' if result else '❌ FAILED'
        print(f'   {test_name.replace("_", " ").title()}: {status}')
    
    print(f'\n🎯 OVERALL RESULT: {passed_tests}/{total_tests} tests passed')
    
    if passed_tests == total_tests:
        print('🏆 CONSOLIDATION SUCCESSFUL - All tests passed!')
        return True
    else:
        print('⚠️  CONSOLIDATION ISSUES - Some tests failed')
        return False

def check_removed_systems():
    """Verify that removed systems are no longer accessible"""
    
    print('\n🔍 CHECKING REMOVED SYSTEMS')
    print('=' * 30)
    
    removed_systems = [
        'core/report_generation_bridge.py',
        'core/perfect_extraction_integration.py',
        'generate_final_corrected_report.py',
        'generate_corrected_employee_report.py',
        'generate_real_employee_report.py',
        'generate_final_specification_compliant_report.py'
    ]
    
    all_removed = True
    
    for system in removed_systems:
        if os.path.exists(system):
            print(f'   ❌ {system}: Still exists (should be removed)')
            all_removed = False
        else:
            print(f'   ✅ {system}: Successfully removed')
    
    # Check backup directory
    backup_dir = 'backup_removed_systems'
    if os.path.exists(backup_dir):
        backup_files = os.listdir(backup_dir)
        print(f'\n📦 Backup directory contains {len(backup_files)} files:')
        for file in backup_files:
            print(f'   • {file}')
    else:
        print('   ❌ Backup directory not found')
        all_removed = False
    
    return all_removed

def main():
    """Main validation function"""
    
    print('🎯 COMPREHENSIVE CONSOLIDATION VALIDATION')
    print('=' * 60)
    print(f'Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    # Test consolidated system
    system_working = test_consolidated_system()
    
    # Check removed systems
    systems_removed = check_removed_systems()
    
    # Final verdict
    print('\n🏆 FINAL CONSOLIDATION VERDICT')
    print('=' * 40)
    
    if system_working and systems_removed:
        print('✅ CONSOLIDATION COMPLETED SUCCESSFULLY!')
        print('   • All functionality working correctly')
        print('   • All redundant systems safely removed')
        print('   • Dictionary filtering operational')
        print('   • Business rules processing active')
        print('   • Multi-format generation working')
        print('\n🎉 The system is now consolidated to a single, powerful reporting engine!')
        return True
    else:
        print('❌ CONSOLIDATION ISSUES DETECTED')
        if not system_working:
            print('   • Consolidated system has issues')
        if not systems_removed:
            print('   • System removal incomplete')
        print('\n⚠️  Please review and address the issues above.')
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
