#!/usr/bin/env python3
"""
ANALYZE COMPARISON RESULTS
==========================

Let's see what's actually in the comparison results and why
tracker feeding can't find any loan-related items.
"""

import sqlite3
import os

def analyze_comparison_results():
    """Analyze what's in the comparison results"""
    print("🔍 ANALYZING COMPARISON RESULTS")
    print("=" * 60)
    
    # Connect to database
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get the latest session
        cursor.execute("""
            SELECT session_id FROM audit_sessions 
            ORDER BY created_at DESC LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return False
        
        session_id = session_result[0]
        print(f"📊 Latest session: {session_id}")
        
        # Get total count
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results WHERE session_id = ?
        """, (session_id,))
        total_count = cursor.fetchone()[0]
        print(f"📊 Total comparison results: {total_count}")
        
        # Sample some results
        cursor.execute("""
            SELECT employee_id, item_label, section_name, current_value, change_type
            FROM comparison_results 
            WHERE session_id = ? 
            ORDER BY employee_id, item_label
            LIMIT 20
        """, (session_id,))
        sample_results = cursor.fetchall()
        
        print(f"\n📋 Sample Comparison Results:")
        for i, (emp_id, item_label, section, current_val, change_type) in enumerate(sample_results):
            print(f"   {i+1:2d}. {emp_id} | {section:12s} | {item_label:30s} | {current_val:10s} | {change_type}")
        
        # Check for any loan-related keywords
        loan_keywords = ['LOAN', 'BALANCE', 'DEDUCTION', 'VEHICLE', 'MOTOR', 'OUTSTANDING']
        
        print(f"\n🔍 SEARCHING FOR LOAN-RELATED ITEMS:")
        for keyword in loan_keywords:
            cursor.execute("""
                SELECT COUNT(*) FROM comparison_results 
                WHERE session_id = ? AND item_label LIKE ?
            """, (session_id, f'%{keyword}%'))
            count = cursor.fetchone()[0]
            print(f"   {keyword}: {count} items")
        
        # Check what sections exist
        cursor.execute("""
            SELECT section_name, COUNT(*) 
            FROM comparison_results 
            WHERE session_id = ? 
            GROUP BY section_name
            ORDER BY COUNT(*) DESC
        """, (session_id,))
        sections = cursor.fetchall()
        
        print(f"\n📊 Sections in Comparison Results:")
        for section, count in sections:
            print(f"   {section}: {count}")
        
        # Check extracted data to see if loans exist there
        print(f"\n🔍 CHECKING EXTRACTED DATA FOR LOANS:")
        cursor.execute("""
            SELECT section_name, COUNT(*) 
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
            GROUP BY section_name
            ORDER BY COUNT(*) DESC
        """, (session_id,))
        extracted_sections = cursor.fetchall()
        
        print(f"📊 Sections in Extracted Data:")
        for section, count in extracted_sections:
            print(f"   {section}: {count}")
        
        # Look for loan items in extracted data
        cursor.execute("""
            SELECT employee_id, item_label, section_name, item_value
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current' AND (
                item_label LIKE '%BALANCE B/F%' OR 
                item_label LIKE '%LOAN%' OR
                section_name = 'LOANS'
            )
            LIMIT 10
        """, (session_id,))
        loan_extracted = cursor.fetchall()
        
        if loan_extracted:
            print(f"\n📋 Loan Items in Extracted Data:")
            for emp_id, item_label, section, value in loan_extracted:
                print(f"   {emp_id} | {section} | {item_label} | {value}")
            
            # These should be in comparison results - let's create them
            return create_loan_comparison_results(cursor, session_id, loan_extracted)
        else:
            print(f"\n❌ No loan items found in extracted data either")
            return False
        
    except Exception as e:
        print(f"❌ Error analyzing comparison results: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

def create_loan_comparison_results(cursor, session_id, loan_extracted):
    """Create loan comparison results from extracted data"""
    print(f"\n🔧 CREATING LOAN COMPARISON RESULTS:")
    
    try:
        from datetime import datetime
        
        created_count = 0
        for emp_id, item_label, section, value in loan_extracted:
            # Check if this item already exists in comparison results
            cursor.execute("""
                SELECT COUNT(*) FROM comparison_results 
                WHERE session_id = ? AND employee_id = ? AND item_label = ?
            """, (session_id, emp_id, item_label))
            
            if cursor.fetchone()[0] == 0:
                # Determine change type based on item
                if 'balance b/f' in item_label.lower():
                    change_type = 'NEW_LOAN'
                elif 'deduction' in item_label.lower():
                    change_type = 'NEW_DEDUCTION'
                else:
                    change_type = 'NEW_LOAN'
                
                # Create comparison result
                cursor.execute("""
                    INSERT INTO comparison_results 
                    (session_id, employee_id, section_name, item_label, current_value, 
                     previous_value, change_type, created_at)
                    VALUES (?, ?, ?, ?, ?, NULL, ?, ?)
                """, (
                    session_id, emp_id, section, item_label, value, 
                    change_type, datetime.now()
                ))
                created_count += 1
                
                if created_count <= 5:
                    print(f"   ✅ Created: {emp_id} - {item_label} ({change_type})")
        
        cursor.connection.commit()
        print(f"   ✅ Created {created_count} loan comparison results")
        
        if created_count > 0:
            # Test tracker feeding
            print(f"\n🔄 TESTING TRACKER FEEDING WITH LOAN DATA:")
            return test_tracker_feeding_with_loans(cursor, session_id)
        else:
            print(f"   ❌ No new loan comparison results created")
            return False
            
    except Exception as e:
        print(f"❌ Error creating loan comparison results: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tracker_feeding_with_loans(cursor, session_id):
    """Test tracker feeding with loan data"""
    try:
        import sys
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        manager.current_month = "06"
        manager.current_year = "2025"
        
        # Clear existing tracker results
        cursor.execute("DELETE FROM tracker_results WHERE session_id = ?", (session_id,))
        cursor.connection.commit()
        
        # Run tracker feeding phase
        print("📊 Running tracker feeding phase...")
        success = manager._phase_tracker_feeding({})
        
        if success:
            print(f"   ✅ Tracker feeding phase completed successfully!")
            
            # Check results
            cursor.execute("""
                SELECT tracker_type, COUNT(*) 
                FROM tracker_results 
                WHERE session_id = ? 
                GROUP BY tracker_type
            """, (session_id,))
            results = cursor.fetchall()
            
            if results:
                print(f"   📊 Tracker Results:")
                for tracker_type, count in results:
                    print(f"     {tracker_type}: {count}")
                return True
            else:
                print(f"   ⚠️ No tracker results generated")
                return False
        else:
            print(f"   ❌ Tracker feeding phase failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing tracker feeding: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 ANALYZING COMPARISON RESULTS")
    print("=" * 70)
    
    success = analyze_comparison_results()
    
    if success:
        print("\n🎉 TRACKER FEEDING NOW WORKING!")
        print("   Loan comparison results created and tracker feeding tested.")
    else:
        print("\n❌ ANALYSIS COMPLETE - MANUAL INTERVENTION NEEDED")
        print("   Check the analysis results above.")
