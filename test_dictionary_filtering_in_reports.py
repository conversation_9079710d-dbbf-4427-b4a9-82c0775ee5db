#!/usr/bin/env python3
"""
Test to verify that all report generation functions respect dictionary filtering.
This ensures that include/exclude toggles and change detection settings are properly applied.
"""

import sqlite3
import os
import subprocess
import sys
import json

def test_dictionary_filtering_in_reports():
    """Test that all report generation respects dictionary filtering"""
    
    print('=== TESTING DICTIONARY FILTERING IN ALL REPORTS ===\n')
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print('❌ Database file not found')
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get current session
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    
    if not session_result:
        print('❌ No audit sessions found')
        return False
    
    session_id = session_result[0]
    print(f'Using session: {session_id}')
    
    # Get total comparison results
    cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
    total_results = cursor.fetchone()[0]
    print(f'Total comparison results: {total_results}')
    
    if total_results == 0:
        print('❌ No comparison results found')
        return False
    
    # Test 1: Create a test scenario by excluding an item from reports
    print('\n1. 🧪 SETTING UP TEST SCENARIO:')
    
    # Find a test item to exclude
    cursor.execute('''
        SELECT DISTINCT cr.section_name, cr.item_label
        FROM comparison_results cr
        WHERE cr.session_id = ?
        LIMIT 1
    ''', (session_id,))
    
    test_item = cursor.fetchone()
    if not test_item:
        print('❌ No test item found')
        return False
    
    test_section, test_item_name = test_item
    print(f'Test item: {test_section}.{test_item_name}')
    
    # Exclude this item from reports
    cursor.execute('''
        UPDATE dictionary_items 
        SET include_in_report = 0 
        WHERE item_name = ? AND section_id IN (
            SELECT id FROM dictionary_sections WHERE section_name = ?
        )
    ''', (test_item_name, test_section))
    
    excluded_rows = cursor.rowcount
    if excluded_rows > 0:
        print(f'✅ Excluded {test_item_name} from reports')
    else:
        # Item might not be in dictionary, add it as excluded
        cursor.execute('SELECT id FROM dictionary_sections WHERE section_name = ?', (test_section,))
        section_result = cursor.fetchone()
        if section_result:
            section_id = section_result[0]
            cursor.execute('''
                INSERT OR REPLACE INTO dictionary_items
                (section_id, item_name, standard_key, include_in_report, include_new, 
                 include_increase, include_decrease, include_removed, include_no_change)
                VALUES (?, ?, ?, 0, 1, 1, 1, 1, 0)
            ''', (section_id, test_item_name, test_item_name.lower().replace(' ', '_')))
            print(f'✅ Added {test_item_name} to dictionary as excluded')
    
    conn.commit()
    
    # Test 2: Check filtered vs unfiltered counts
    print('\n2. 📊 TESTING FILTERING EFFECTIVENESS:')
    
    # Unfiltered count
    cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
    unfiltered_count = cursor.fetchone()[0]
    
    # Filtered count (using the same logic as report generation)
    cursor.execute('''
        SELECT COUNT(*)
        FROM comparison_results cr
        LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
        LEFT JOIN dictionary_sections ds ON di.section_id = ds.id AND cr.section_name = ds.section_name
        WHERE cr.session_id = ?
        AND (
            -- Include items that are marked to be included in reports
            (di.include_in_report = 1 OR di.include_in_report IS NULL)
            AND
            -- Apply change detection filtering based on change type
            (
                (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                (cr.change_type = 'INCREASED' AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                (cr.change_type = 'DECREASED' AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1 OR di.include_no_change IS NULL)) OR
                (cr.change_type NOT IN ('NEW', 'INCREASED', 'DECREASED', 'REMOVED', 'NO_CHANGE'))
            )
        )
    ''', (session_id,))
    
    filtered_count = cursor.fetchone()[0]
    
    print(f'Unfiltered results: {unfiltered_count}')
    print(f'Filtered results: {filtered_count}')
    
    if filtered_count < unfiltered_count:
        print('✅ Filtering is working - some items excluded')
    else:
        print('⚠️  No items filtered out')
    
    # Test 3: Test specific report generation functions
    print('\n3. 🧪 TESTING REPORT GENERATION FUNCTIONS:')
    
    test_functions = [
        ('core/report_generation_bridge.py', ['generate-report', session_id]),
        ('core/advanced_report_generator.py', ['generate-both', session_id]),
        ('generate_final_corrected_report.py', [session_id]),
        ('generate_corrected_employee_report.py', [session_id]),
        ('generate_real_employee_report.py', [session_id])
    ]
    
    all_tests_passed = True
    
    for script_path, args in test_functions:
        if os.path.exists(script_path):
            print(f'\n   Testing {script_path}...')
            try:
                result = subprocess.run(
                    [sys.executable, script_path] + args,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    # Check if the output mentions filtering
                    output = result.stdout.lower()
                    if 'filtered' in output or 'dictionary' in output:
                        print(f'   ✅ {script_path} - Filtering applied')
                    else:
                        print(f'   ⚠️  {script_path} - No filtering evidence in output')
                else:
                    print(f'   ❌ {script_path} - Failed: {result.stderr[:100]}')
                    all_tests_passed = False
                    
            except Exception as e:
                print(f'   ❌ {script_path} - Error: {str(e)[:100]}')
                all_tests_passed = False
        else:
            print(f'   ⚠️  {script_path} - File not found')
    
    # Test 4: Restore original state
    print('\n4. 🔄 RESTORING ORIGINAL STATE:')
    
    # Restore the test item
    cursor.execute('''
        UPDATE dictionary_items 
        SET include_in_report = 1 
        WHERE item_name = ? AND section_id IN (
            SELECT id FROM dictionary_sections WHERE section_name = ?
        )
    ''', (test_item_name, test_section))
    
    conn.commit()
    print(f'✅ Restored {test_item_name} to included state')
    
    # Final verification
    cursor.execute('''
        SELECT COUNT(*)
        FROM comparison_results cr
        LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
        LEFT JOIN dictionary_sections ds ON di.section_id = ds.id AND cr.section_name = ds.section_name
        WHERE cr.session_id = ?
        AND (
            (di.include_in_report = 1 OR di.include_in_report IS NULL)
            AND
            (
                (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                (cr.change_type = 'INCREASED' AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                (cr.change_type = 'DECREASED' AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1 OR di.include_no_change IS NULL)) OR
                (cr.change_type NOT IN ('NEW', 'INCREASED', 'DECREASED', 'REMOVED', 'NO_CHANGE'))
            )
        )
    ''', (session_id,))
    
    final_count = cursor.fetchone()[0]
    print(f'Final filtered count: {final_count}')
    
    conn.close()
    
    # Summary
    print('\n5. 📋 SUMMARY:')
    if all_tests_passed:
        print('✅ ALL REPORT GENERATION FUNCTIONS RESPECT DICTIONARY FILTERING')
        print('✅ Include/exclude toggles are working')
        print('✅ Change detection settings are applied')
        return True
    else:
        print('❌ SOME REPORT GENERATION FUNCTIONS DO NOT RESPECT FILTERING')
        print('❌ Risk of showing excluded items in reports')
        return False

if __name__ == '__main__':
    success = test_dictionary_filtering_in_reports()
    sys.exit(0 if success else 1)
