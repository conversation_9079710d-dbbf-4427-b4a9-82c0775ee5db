#!/usr/bin/env python3
"""
DEBUG COMPARISON DATA LOADING ISSUE
====================================

The extraction phase shows "211 current, 208 previous employees stored" but
the comparison phase fails with "No extracted data found for auditing".

This suggests a disconnect between data storage and data loading.
"""

import sqlite3
import os
import sys
from datetime import datetime

def debug_comparison_data_loading():
    """Debug the comparison data loading issue"""
    print("🔍 DEBUGGING COMPARISON DATA LOADING ISSUE")
    print("=" * 60)
    
    # Connect to database
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get the latest session
        cursor.execute("""
            SELECT session_id, created_at
            FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return False
        
        session_id, created_at = session_result
        print(f"📊 Latest session: {session_id}")
        print(f"📅 Created: {created_at}")
        
        # Check extracted data in detail
        cursor.execute("""
            SELECT period_type, COUNT(*) as total_records, COUNT(DISTINCT employee_id) as unique_employees,
                   MIN(created_at) as first_record, MAX(created_at) as last_record
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY period_type
        """, (session_id,))
        extraction_results = cursor.fetchall()
        
        print(f"\n📋 Detailed Extraction Results:")
        for period, total_records, unique_employees, first_record, last_record in extraction_results:
            print(f"   {period}:")
            print(f"     Total Records: {total_records}")
            print(f"     Unique Employees: {unique_employees}")
            print(f"     First Record: {first_record}")
            print(f"     Last Record: {last_record}")
        
        # Check if data exists but is malformed
        cursor.execute("""
            SELECT period_type, employee_id, employee_name, section_name, item_label, item_value
            FROM extracted_data 
            WHERE session_id = ? 
            LIMIT 10
        """, (session_id,))
        sample_data = cursor.fetchall()
        
        print(f"\n📋 Sample Data (first 10 records):")
        for period, emp_id, emp_name, section, item, value in sample_data:
            print(f"   {period}: {emp_id} | {emp_name} | {section} | {item} | {value}")
        
        # Test the data loading logic directly
        print(f"\n🔧 TESTING DATA LOADING LOGIC:")
        return test_data_loading_logic(cursor, session_id)
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

def test_data_loading_logic(cursor, session_id):
    """Test the data loading logic that the comparison phase uses"""
    try:
        # Import the phased process manager
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        print("📊 Testing _load_extracted_data method...")
        
        # Test loading current data
        print("   Loading current data...")
        current_data = manager._load_extracted_data('current')
        print(f"   Current data loaded: {len(current_data) if current_data else 0} employees")
        
        if current_data:
            print(f"   Sample current employee: {list(current_data.keys())[0] if current_data else 'None'}")
            sample_emp = list(current_data.values())[0] if current_data else None
            if sample_emp:
                print(f"   Sample employee data keys: {list(sample_emp.keys())}")
        
        # Test loading previous data
        print("   Loading previous data...")
        previous_data = manager._load_extracted_data('previous')
        print(f"   Previous data loaded: {len(previous_data) if previous_data else 0} employees")
        
        if previous_data:
            print(f"   Sample previous employee: {list(previous_data.keys())[0] if previous_data else 'None'}")
            sample_emp = list(previous_data.values())[0] if previous_data else None
            if sample_emp:
                print(f"   Sample employee data keys: {list(sample_emp.keys())}")
        
        # Check what the comparison logic expects
        print(f"\n🔍 COMPARISON LOGIC ANALYSIS:")
        if not current_data:
            print("   ❌ Current data is empty - this causes 'No extracted data found for auditing'")
            return fix_data_loading_issue(cursor, session_id, manager)
        elif not previous_data:
            print("   ❌ Previous data is empty - this causes comparison failure")
            return fix_data_loading_issue(cursor, session_id, manager)
        else:
            print("   ✅ Both current and previous data loaded successfully")
            print("   🤔 The issue might be in the comparison logic itself")
            return test_comparison_logic(manager, current_data, previous_data)
            
    except Exception as e:
        print(f"❌ Error testing data loading logic: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_data_loading_issue(cursor, session_id, manager):
    """Fix the data loading issue"""
    print(f"\n🔧 FIXING DATA LOADING ISSUE")
    print("-" * 30)
    
    try:
        # Check the raw database structure
        cursor.execute("""
            SELECT COUNT(*) FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
        """, (session_id,))
        current_count = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM extracted_data 
            WHERE session_id = ? AND period_type = 'previous'
        """, (session_id,))
        previous_count = cursor.fetchone()[0]
        
        print(f"   Raw database counts: current={current_count}, previous={previous_count}")
        
        if current_count == 0:
            print("   ❌ No current data in database")
            return False
        
        if previous_count == 0:
            print("   ❌ No previous data in database - using our created data")
            # The previous data should exist from our earlier script
            return False
        
        # Check if the _load_extracted_data method has issues
        print("   🔍 Analyzing _load_extracted_data method...")
        
        # Try manual data loading
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
            ORDER BY employee_id, section_name, item_label
        """, (session_id,))
        current_raw = cursor.fetchall()
        
        print(f"   Manual current data query: {len(current_raw)} records")
        
        if len(current_raw) > 0:
            # Group manually
            current_manual = {}
            for emp_id, emp_name, section, item, value, numeric_val in current_raw:
                if emp_id not in current_manual:
                    current_manual[emp_id] = {
                        'employee_name': emp_name,
                        'items': {}
                    }
                
                if section not in current_manual[emp_id]['items']:
                    current_manual[emp_id]['items'][section] = {}
                
                current_manual[emp_id]['items'][section][item] = {
                    'value': value,
                    'numeric_value': numeric_val
                }
            
            print(f"   Manual grouping result: {len(current_manual)} employees")
            
            if len(current_manual) > 0:
                print("   ✅ Manual data loading works - issue is in _load_extracted_data method")
                return patch_data_loading_method(manager, session_id)
            else:
                print("   ❌ Manual data loading also fails")
                return False
        else:
            print("   ❌ No current data found in manual query")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing data loading issue: {e}")
        import traceback
        traceback.print_exc()
        return False

def patch_data_loading_method(manager, session_id):
    """Patch the data loading method"""
    print(f"\n🔧 PATCHING DATA LOADING METHOD")
    print("-" * 30)
    
    try:
        # Override the _load_extracted_data method with a working version
        def patched_load_extracted_data(period_type):
            print(f"   📊 Loading {period_type} data with patched method...")
            
            import sqlite3
            db_path = os.path.join('data', 'templar_payroll_auditor.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value, department
                    FROM extracted_data 
                    WHERE session_id = ? AND period_type = ?
                    ORDER BY employee_id, section_name, item_label
                """, (session_id, period_type))
                raw_data = cursor.fetchall()
                
                print(f"   Raw query returned {len(raw_data)} records")
                
                if not raw_data:
                    return {}
                
                # Group by employee
                employees = {}
                for emp_id, emp_name, section, item, value, numeric_val, dept in raw_data:
                    if emp_id not in employees:
                        employees[emp_id] = {
                            'employee_name': emp_name,
                            'department': dept or 'Unknown',
                            'items': {}
                        }
                    
                    if section not in employees[emp_id]['items']:
                        employees[emp_id]['items'][section] = {}
                    
                    employees[emp_id]['items'][section][item] = {
                        'value': value,
                        'numeric_value': numeric_val
                    }
                
                print(f"   Grouped into {len(employees)} employees")
                return employees
                
            finally:
                conn.close()
        
        # Patch the method
        manager._load_extracted_data = patched_load_extracted_data
        
        # Test the patched method
        current_data = manager._load_extracted_data('current')
        previous_data = manager._load_extracted_data('previous')
        
        print(f"   Patched method results: current={len(current_data)}, previous={len(previous_data)}")
        
        if current_data and previous_data:
            print("   ✅ Patched method works - running comparison...")
            return test_comparison_logic(manager, current_data, previous_data)
        else:
            print("   ❌ Patched method still fails")
            return False
            
    except Exception as e:
        print(f"❌ Error patching data loading method: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comparison_logic(manager, current_data, previous_data):
    """Test the comparison logic"""
    print(f"\n🔄 TESTING COMPARISON LOGIC")
    print("-" * 30)
    
    try:
        print(f"   Input: {len(current_data)} current, {len(previous_data)} previous employees")
        
        # Run comparison
        comparison_results = manager._compare_payroll_data(current_data, previous_data)
        
        print(f"   Output: {len(comparison_results)} comparison results")
        
        if comparison_results:
            # Store results
            print("   💾 Storing comparison results...")
            manager._store_comparison_results(comparison_results)
            
            print("   ✅ Comparison logic works successfully!")
            return True
        else:
            print("   ❌ Comparison logic returned no results")
            return False
            
    except Exception as e:
        print(f"❌ Error testing comparison logic: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 DEBUGGING COMPARISON DATA LOADING ISSUE")
    print("=" * 70)
    
    success = debug_comparison_data_loading()
    
    if success:
        print("\n🎉 COMPARISON DATA LOADING ISSUE RESOLVED!")
        print("   The comparison phase should now work correctly.")
        print("   Try running the audit process again.")
    else:
        print("\n❌ COULD NOT RESOLVE COMPARISON DATA LOADING ISSUE")
        print("   Further investigation required.")
