#!/usr/bin/env python3
"""
Production Monitoring Dashboard for Dictionary Manager Toggle Compliance
Real-time monitoring of filtering effectiveness and system health
"""

import sqlite3
import os
import json
from datetime import datetime, timedelta
from core.dictionary_filter_manager import DictionaryFilterManager

def generate_production_dashboard():
    """Generate comprehensive production monitoring dashboard"""
    
    print('🎯 DICTIONARY MANAGER PRODUCTION MONITORING DASHBOARD')
    print('=' * 70)
    print(f'Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print()
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print('❌ Database not found')
        return False
    
    manager = DictionaryFilterManager(db_path)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. System Health Check
        print('🏥 SYSTEM HEALTH CHECK')
        print('-' * 30)
        
        # Check recent sessions
        cursor.execute('''
            SELECT session_id, created_at, 
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = audit_sessions.session_id) as result_count
            FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 5
        ''')
        
        recent_sessions = cursor.fetchall()
        
        if recent_sessions:
            print('✅ Recent audit sessions:')
            for session_id, created_at, result_count in recent_sessions:
                print(f'   • {session_id}: {created_at} ({result_count} results)')
        else:
            print('⚠️  No recent audit sessions found')
        
        # 2. Dictionary Configuration Health
        print('\n🔧 DICTIONARY CONFIGURATION HEALTH')
        print('-' * 40)
        
        # Check for configuration conflicts
        cursor.execute('''
            SELECT ds.section_name, di.item_name, di.include_in_report,
                   di.include_new, di.include_increase, di.include_decrease, 
                   di.include_removed, di.include_no_change
            FROM dictionary_items di
            JOIN dictionary_sections ds ON di.section_id = ds.id
            WHERE (
                -- Type 1 conflicts: include_in_report=0 but change detection enabled
                (di.include_in_report = 0 AND (di.include_new = 1 OR di.include_increase = 1 OR 
                 di.include_decrease = 1 OR di.include_removed = 1 OR di.include_no_change = 1))
                OR
                -- Type 2 conflicts: include_in_report=1 but all change detection disabled
                (di.include_in_report = 1 AND di.include_new = 0 AND di.include_increase = 0 AND 
                 di.include_decrease = 0 AND di.include_removed = 0 AND di.include_no_change = 0)
            )
        ''')
        
        conflicts = cursor.fetchall()
        
        if conflicts:
            print(f'⚠️  Found {len(conflicts)} toggle configuration conflicts:')
            for section, item, include_report, new, inc, dec, rem, no_change in conflicts:
                if include_report == 0:
                    enabled = [t for t, v in [('NEW', new), ('INC', inc), ('DEC', dec), ('REM', rem), ('NO_CHANGE', no_change)] if v == 1]
                    print(f'   • {section}.{item}: Excluded but has enabled: {enabled}')
                else:
                    print(f'   • {section}.{item}: Included but all change detection disabled')
        else:
            print('✅ No toggle configuration conflicts found')
        
        # 3. Filtering Effectiveness Analysis
        print('\n📊 FILTERING EFFECTIVENESS ANALYSIS')
        print('-' * 40)
        
        if recent_sessions:
            latest_session = recent_sessions[0][0]
            stats = manager.get_filtering_statistics(latest_session)
            
            if 'error' not in stats:
                print(f'Latest Session: {latest_session}')
                print(f'✅ Total Results: {stats["total_results"]:,}')
                print(f'✅ Filtered Results: {stats["filtered_results"]:,}')
                print(f'✅ Excluded Results: {stats["excluded_results"]:,}')
                print(f'✅ Reduction: {stats["reduction_percentage"]}%')
                print(f'✅ Effectiveness: {stats["filtering_effectiveness"]}')
                
                print('\n📈 Change Type Breakdown:')
                for change_type, count in stats["change_type_breakdown"].items():
                    print(f'   • {change_type}: {count:,}')
                
                print('\n🚫 Exclusion Reasons:')
                for reason, count in stats["exclusion_breakdown"].items():
                    print(f'   • {reason}: {count:,}')
            else:
                print(f'❌ Error getting statistics: {stats["error"]}')
        
        # 4. Report Generation Status
        print('\n📄 REPORT GENERATION STATUS')
        print('-' * 35)
        
        # Check recent reports
        cursor.execute('''
            SELECT report_id, report_category, created_at, file_paths
            FROM reports 
            ORDER BY created_at DESC 
            LIMIT 5
        ''')
        
        recent_reports = cursor.fetchall()
        
        if recent_reports:
            print('✅ Recent reports generated:')
            for report_id, category, created_at, file_paths in recent_reports:
                files = json.loads(file_paths) if file_paths else {}
                file_count = len([f for f in files.values() if f])
                print(f'   • {report_id}: {category} ({file_count} files) - {created_at}')
        else:
            print('⚠️  No recent reports found')
        
        # 5. Performance Metrics
        print('\n⚡ PERFORMANCE METRICS')
        print('-' * 25)
        
        # Database size
        db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB
        print(f'✅ Database Size: {db_size:.1f} MB')
        
        # Table counts
        tables = ['comparison_results', 'dictionary_items', 'audit_sessions', 'reports']
        for table in tables:
            cursor.execute(f'SELECT COUNT(*) FROM {table}')
            count = cursor.fetchone()[0]
            print(f'✅ {table}: {count:,} records')
        
        # 6. Recommendations
        print('\n💡 RECOMMENDATIONS')
        print('-' * 20)
        
        recommendations = []
        
        if conflicts:
            recommendations.append(f"Resolve {len(conflicts)} toggle configuration conflicts")
        
        if recent_sessions and len(recent_sessions) < 3:
            recommendations.append("Consider running more frequent audits for better monitoring")
        
        if db_size > 100:
            recommendations.append("Database size is large - consider archiving old data")
        
        if not recent_reports:
            recommendations.append("No recent reports found - verify report generation is working")
        
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f'{i}. {rec}')
        else:
            print('✅ System is operating optimally')
        
        # 7. System Status Summary
        print('\n🎯 SYSTEM STATUS SUMMARY')
        print('-' * 30)
        
        status_items = [
            ('Database Connection', '✅ Connected'),
            ('Recent Sessions', f'✅ {len(recent_sessions)} found' if recent_sessions else '⚠️  None found'),
            ('Configuration Conflicts', f'⚠️  {len(conflicts)} found' if conflicts else '✅ None found'),
            ('Filtering Effectiveness', f'✅ {stats.get("filtering_effectiveness", "Unknown")}' if 'stats' in locals() and 'error' not in stats else '❌ Error'),
            ('Recent Reports', f'✅ {len(recent_reports)} found' if recent_reports else '⚠️  None found')
        ]
        
        for item, status in status_items:
            print(f'{item}: {status}')
        
        # Overall health score
        health_score = 0
        if recent_sessions: health_score += 20
        if not conflicts: health_score += 30
        if 'stats' in locals() and 'error' not in stats and stats.get('filtering_effectiveness') == 'High': health_score += 30
        if recent_reports: health_score += 20
        
        print(f'\n🏥 Overall System Health: {health_score}/100')
        
        if health_score >= 80:
            print('✅ EXCELLENT - System is operating at optimal levels')
        elif health_score >= 60:
            print('⚠️  GOOD - System is stable with minor issues')
        elif health_score >= 40:
            print('⚠️  FAIR - System needs attention')
        else:
            print('❌ POOR - System requires immediate attention')
        
        return health_score >= 60
        
    except Exception as e:
        print(f'❌ Dashboard generation failed: {e}')
        return False
    finally:
        conn.close()

def main():
    """Main function for command line usage"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--json':
        # JSON output for automated monitoring
        manager = DictionaryFilterManager()
        db_path = os.path.join('data', 'templar_payroll_auditor.db')
        
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
            session_result = cursor.fetchone()
            
            if session_result:
                stats = manager.get_filtering_statistics(session_result[0])
                print(json.dumps(stats, indent=2))
            else:
                print(json.dumps({"error": "No sessions found"}))
            
            conn.close()
        else:
            print(json.dumps({"error": "Database not found"}))
    else:
        # Human-readable dashboard
        generate_production_dashboard()

if __name__ == "__main__":
    main()
