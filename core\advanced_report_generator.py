#!/usr/bin/env python3
"""
Advanced Report Generator - Creates proper item-based and employee-based reports
"""

import os
import sys
import json
import sqlite3
from datetime import datetime
from pathlib import Path
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

# Add parent directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

def get_session_data(session_id=None):
    """Get session data from database"""
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get latest session if not provided
        if not session_id:
            cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
            result = cursor.fetchone()
            if result:
                session_id = result[0]
            else:
                return None, None
        
        # Get comparison results (for business rules processing)
        # CRITICAL FIX: Apply dictionary filtering to respect include/exclude toggles and change detection settings
        cursor.execute('''
            SELECT cr.employee_id, cr.employee_name, cr.section_name, cr.item_label,
                   cr.previous_value, cr.current_value, cr.change_type, cr.priority
            FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            LEFT JOIN dictionary_sections ds ON di.section_id = ds.id AND cr.section_name = ds.section_name
            WHERE cr.session_id = ?
            AND (
                -- Include items that are marked to be included in reports
                (di.include_in_report = 1 OR di.include_in_report IS NULL)
                AND
                -- Apply change detection filtering based on change type
                (
                    (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                    (cr.change_type = 'INCREASED' AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                    (cr.change_type = 'DECREASED' AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                    (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                    (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1 OR di.include_no_change IS NULL)) OR
                    (cr.change_type NOT IN ('NEW', 'INCREASED', 'DECREASED', 'REMOVED', 'NO_CHANGE'))
                )
            )
            ORDER BY cr.employee_id, cr.section_name, cr.item_label
        ''', (session_id,))

        comparison_data = cursor.fetchall()
        
        # Get session metadata
        cursor.execute('''
            SELECT current_pdf_path, previous_pdf_path, created_at
            FROM audit_sessions 
            WHERE session_id = ?
        ''', (session_id,))
        
        session_info = cursor.fetchone()
        conn.close()
        
        return comparison_data, session_info
        
    except Exception as e:
        print(f"Error getting session data: {e}", file=sys.stderr)
        return None, None

def apply_business_rules(comparison_data, debug=True):
    """Apply business rules to comparison data"""
    if debug:
        print("   Applying business rules to comparison data...", file=sys.stderr)
    
    # Convert to format expected by business rules
    changes = []
    for row in comparison_data:
        change = {
            'employee_id': row[0] or 'Unknown',
            'employee_name': row[1] or 'Unknown Employee',
            'section_name': row[2] or 'Unknown Section',
            'item_name': row[3] or 'Unknown Item',
            'previous_value': row[4] or '0',
            'current_value': row[5] or '0',
            'change_type': row[6] or 'UNKNOWN',
            'priority': row[7] or 'LOW',
            'department': row[2] or 'Unknown Department'  # Using section as department
        }
        changes.append(change)
    
    # Apply business rules (simplified version)
    processed_changes = []
    for change in changes:
        # Determine change type
        prev_val = float(change['previous_value']) if change['previous_value'].replace('.', '').isdigit() else 0
        curr_val = float(change['current_value']) if change['current_value'].replace('.', '').isdigit() else 0
        
        if prev_val == 0 and curr_val > 0:
            change_type = 'NEW'
            narration = f"NEW {change['item_name']} of GHS {curr_val:,.2f} added to payslip"
        elif prev_val > 0 and curr_val == 0:
            change_type = 'REMOVED'
            narration = f"{change['item_name']} of GHS {prev_val:,.2f} removed from payslip"
        elif curr_val > prev_val:
            change_type = 'INCREASED'
            difference = curr_val - prev_val
            narration = f"{change['item_name']} increased from GHS {prev_val:,.2f} to GHS {curr_val:,.2f} (increase of GHS {difference:,.2f})"
        elif curr_val < prev_val:
            change_type = 'DECREASED'
            difference = prev_val - curr_val
            narration = f"{change['item_name']} decreased from GHS {prev_val:,.2f} to GHS {curr_val:,.2f} (decrease of GHS {difference:,.2f})"
        else:
            continue  # Skip no-change items
        
        processed_change = {
            **change,
            'change_type': change_type,
            'narration': narration,
            'include_in_report': True
        }
        processed_changes.append(processed_change)
    
    return processed_changes

def generate_employee_based_report(processed_changes, session_id, debug=True):
    """Generate employee-based report as a proper Word document"""
    if debug:
        print("   Generating employee-based report...", file=sys.stderr)

    # Create a new Word document
    doc = Document()

    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)

    # Group by employee
    employees = {}
    for change in processed_changes:
        emp_id = change['employee_id']
        if emp_id not in employees:
            employees[emp_id] = {
                'employee_name': change['employee_name'],
                'department': change['department'],
                'changes': []
            }
        employees[emp_id]['changes'].append(change)

    # Generate report content
    current_date = datetime.now()
    month_year = current_date.strftime('%B %Y').upper()

    # Add title
    title = doc.add_heading(f'PAYROLL AUDIT REPORT: {month_year}', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Create summary table
    summary_table = doc.add_table(rows=5, cols=2)
    summary_table.style = 'Table Grid'

    # Set table headers
    header_row = summary_table.rows[0]
    header_row.cells[0].text = 'Report Information'
    header_row.cells[1].text = 'Executive Summary'

    # Make headers bold
    for cell in header_row.cells:
        for paragraph in cell.paragraphs:
            for run in paragraph.runs:
                run.bold = True

    # Populate summary table
    summary_data = [
        (f'Period: {month_year}', f'Total Employees with Changes: {len(employees)}'),
        (f'Generated: {current_date.strftime("%Y-%m-%d %H:%M:%S")}', f'Total Changes Detected: {len(processed_changes)}'),
        (f'Session ID: {session_id}', 'Report Type: Employee-Based Analysis'),
        ('Generated By: System', 'Status: Completed')
    ]

    for i, (left_text, right_text) in enumerate(summary_data, 1):
        row = summary_table.rows[i]
        row.cells[0].text = left_text
        row.cells[1].text = right_text

    # Add findings section
    doc.add_heading('Findings and Observations', level=1)

    # Add employee findings
    for emp_id, emp_data in sorted(employees.items()):
        if emp_data['changes']:
            # Add employee header
            emp_header = doc.add_paragraph()
            emp_run = emp_header.add_run(f"{emp_id}: {emp_data['employee_name']} – {emp_data['department']}")
            emp_run.bold = True
            emp_header.style = 'Heading 2'

            # Add changes for this employee
            for i, change in enumerate(emp_data['changes'], 1):
                change_para = doc.add_paragraph(f"{i}. {change['narration']}")
                change_para.paragraph_format.left_indent = Inches(0.5)

    return doc

def generate_item_based_report(processed_changes, session_id, debug=True):
    """Generate item-based report as a proper Word document"""
    if debug:
        print("   Generating item-based report...", file=sys.stderr)

    # Create a new Word document
    doc = Document()

    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)

    # Group by item
    items = {}
    for change in processed_changes:
        item_key = f"{change['item_name']}_{change['change_type']}"
        if item_key not in items:
            items[item_key] = {
                'item_name': change['item_name'],
                'change_type': change['change_type'],
                'section_name': change['section_name'],
                'employees': []
            }
        items[item_key]['employees'].append(change)

    # Generate report content
    current_date = datetime.now()
    month_year = current_date.strftime('%B %Y').upper()

    # Add title
    title = doc.add_heading(f'PAYROLL AUDIT REPORT: {month_year}', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Create summary table
    summary_table = doc.add_table(rows=5, cols=2)
    summary_table.style = 'Table Grid'

    # Set table headers
    header_row = summary_table.rows[0]
    header_row.cells[0].text = 'Report Information'
    header_row.cells[1].text = 'Executive Summary'

    # Make headers bold
    for cell in header_row.cells:
        for paragraph in cell.paragraphs:
            for run in paragraph.runs:
                run.bold = True

    # Populate summary table
    summary_data = [
        (f'Period: {month_year}', f'Total Items with Changes: {len(items)}'),
        (f'Generated: {current_date.strftime("%Y-%m-%d %H:%M:%S")}', f'Total Changes Detected: {len(processed_changes)}'),
        (f'Session ID: {session_id}', 'Report Type: Item-Based Analysis'),
        ('Generated By: System', 'Status: Completed')
    ]

    for i, (left_text, right_text) in enumerate(summary_data, 1):
        row = summary_table.rows[i]
        row.cells[0].text = left_text
        row.cells[1].text = right_text

    # Add findings section
    doc.add_heading('Findings and Observations', level=1)

    # Add item findings
    for item_key, item_data in sorted(items.items()):
        change_type_text = item_data['change_type'].lower()
        month_name = current_date.strftime('%B')

        # Add item header
        item_header = doc.add_paragraph()
        item_run = item_header.add_run(f"{item_data['item_name']} {change_type_text} in {month_name} Payslip for the following:")
        item_run.bold = True
        item_header.style = 'Heading 2'

        # Add employees for this item
        for change in item_data['employees']:
            emp_para = doc.add_paragraph(f"• {change['employee_id']}: {change['employee_name']} – {change['department']}")
            emp_para.paragraph_format.left_indent = Inches(0.5)

    return doc

def generate_advanced_reports(session_id=None, report_type='both', debug=True):
    """Generate advanced reports with business rules"""
    if debug:
        print("GENERATING ADVANCED REPORTS", file=sys.stderr)
        print("=" * 50, file=sys.stderr)
    
    try:
        # Get session data
        comparison_data, session_info = get_session_data(session_id)
        
        if not comparison_data:
            return {"success": False, "error": "No session data found"}
        
        if debug:
            print(f"   Using session: {session_id}", file=sys.stderr)
            print(f"   Found {len(comparison_data)} comparison results (after dictionary filtering)", file=sys.stderr)
            print(f"   Dictionary filtering applied: include_in_report + change detection toggles", file=sys.stderr)

        # Apply business rules
        processed_changes = apply_business_rules(comparison_data, debug=debug)
        if debug:
            print(f"   Processed {len(processed_changes)} changes with business rules", file=sys.stderr)
        
        # Create reports directory
        reports_dir = Path('reports')
        reports_dir.mkdir(exist_ok=True)
        
        # Generate timestamp for unique filenames
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        generated_files = {}
        
        # Generate employee-based report
        if report_type in ['both', 'employee']:
            if debug:
                print("   Creating employee-based report...", file=sys.stderr)
            employee_doc = generate_employee_based_report(processed_changes, session_id, debug=debug)

            employee_word_path = reports_dir / f'Employee_Based_Report_{timestamp}.docx'
            employee_doc.save(employee_word_path)

            generated_files['employee_word'] = str(employee_word_path)
            if debug:
                print(f"   Employee-based Word report created: {employee_word_path}", file=sys.stderr)
        
        # Generate item-based report
        if report_type in ['both', 'item']:
            if debug:
                print("   Creating item-based report...", file=sys.stderr)
            item_doc = generate_item_based_report(processed_changes, session_id, debug=debug)

            item_word_path = reports_dir / f'Item_Based_Report_{timestamp}.docx'
            item_doc.save(item_word_path)

            generated_files['item_word'] = str(item_word_path)
            if debug:
                print(f"   Item-based Word report created: {item_word_path}", file=sys.stderr)
        
        # Save to database
        report_id = f'advanced_reports_{timestamp}'
        
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        metadata = {
            "session_id": session_id,
            "generated_at": datetime.now().isoformat(),
            "total_changes": len(processed_changes),
            "report_types": report_type,
            "generation_method": "Advanced Report Generator with Business Rules"
        }
        
        total_size = sum(Path(path).stat().st_size for path in generated_files.values())
        
        cursor.execute('''
            INSERT INTO reports 
            (report_id, report_type, report_category, title, description, file_paths, metadata, file_size)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            report_id,
            'ADVANCED_PAYROLL_AUDIT_REPORTS',
            'Business Rules Processed',
            f'Advanced Payroll Audit Reports - {timestamp}',
            f'Employee-based and Item-based reports generated with business rules processing',
            json.dumps(generated_files),
            json.dumps(metadata),
            total_size
        ))
        
        conn.commit()
        conn.close()
        
        if debug:
            print(f"   Reports saved to database: {report_id}", file=sys.stderr)
        
        # Get filtering statistics for enhanced response
        from core.dictionary_filter_manager import DictionaryFilterManager
        filter_manager = DictionaryFilterManager()
        filtering_stats = filter_manager.get_filtering_statistics(session_id)

        return {
            "success": True,
            "report_id": report_id,
            "files": generated_files,
            "processed_changes": len(processed_changes),
            "filtering_stats": {
                "total_results": filtering_stats.get("total_results", 0),
                "filtered_results": filtering_stats.get("filtered_results", 0),
                "reduction_percentage": filtering_stats.get("reduction_percentage", 0),
                "filtering_effectiveness": filtering_stats.get("filtering_effectiveness", "Unknown")
            },
            "message": f"Advanced reports generated successfully with {filtering_stats.get('reduction_percentage', 0)}% filtering reduction"
        }
        
    except Exception as e:
        if debug:
            print(f"   Error generating advanced reports: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "generate-employee":
            session_id = sys.argv[2] if len(sys.argv) > 2 else None
            result = generate_advanced_reports(session_id, 'employee', debug=False)
            print(json.dumps(result))
        elif command == "generate-item":
            session_id = sys.argv[2] if len(sys.argv) > 2 else None
            result = generate_advanced_reports(session_id, 'item', debug=False)
            print(json.dumps(result))
        elif command == "generate-both":
            session_id = sys.argv[2] if len(sys.argv) > 2 else None
            result = generate_advanced_reports(session_id, 'both', debug=False)
            print(json.dumps(result))
        else:
            print(json.dumps({"success": False, "error": f"Unknown command: {command}"}))
    else:
        # Default: generate both types
        result = generate_advanced_reports(None, 'both', debug=False)
        print(json.dumps(result))
