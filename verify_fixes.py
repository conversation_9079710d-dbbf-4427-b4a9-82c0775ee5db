#!/usr/bin/env python3
"""
Simple verification script for the two fixes
"""

def verify_auto_learning_fix():
    """Verify auto-learning is re-enabled"""
    print("🤖 VERIFYING AUTO-LEARNING FIX")
    print("-" * 40)
    
    try:
        with open('core/hybrid_extraction_integration.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check if auto-learning is disabled
        if 'self.auto_learning = None' in content and 'DEPRECATED' in content:
            # Check if it's in the old deprecated section or new enabled section
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'self.auto_learning = None' in line:
                    # Check context - is this in the exception handler?
                    context = '\n'.join(lines[max(0, i-5):i+5])
                    if 'except Exception' in context:
                        print("✅ Auto-learning disabled only in exception handler (correct)")
                    else:
                        print("❌ Auto-learning still disabled in main flow")
                        return False
                        
        # Check for re-enablement code
        if 'EnhancedDictionaryAutoLearning' in content and 'PRODUCTION FIX: Re-enable' in content:
            print("✅ Auto-learning re-enablement code found")
        else:
            print("❌ Auto-learning re-enablement code not found")
            return False
            
        # Check for session initialization
        if 'start_auto_learning_session' in content:
            print("✅ Auto-learning session initialization found")
        else:
            print("❌ Auto-learning session initialization not found")
            return False
            
        print("✅ AUTO-LEARNING FIX VERIFIED")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying auto-learning fix: {e}")
        return False

def verify_period_acquired_fix():
    """Verify period_acquired uses dynamic dates"""
    print("\n📅 VERIFYING PERIOD ACQUIRED FIX")
    print("-" * 40)
    
    try:
        with open('core/phased_process_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Count hardcoded dates
        hardcoded_count = content.count("'2025-06'")
        print(f"Hardcoded '2025-06' instances: {hardcoded_count}")
        
        if hardcoded_count > 0:
            print("❌ Still contains hardcoded dates")
            return False
        else:
            print("✅ No hardcoded '2025-06' found")
            
        # Check for dynamic period code
        dynamic_pattern = 'f"{self.current_year}-{self.current_month:02d}"'
        if dynamic_pattern in content:
            count = content.count(dynamic_pattern)
            print(f"✅ Dynamic period pattern found {count} times")
        else:
            print("❌ Dynamic period pattern not found")
            return False
            
        # Check for PRODUCTION FIX comments
        if 'PRODUCTION FIX: Use current payroll period' in content:
            print("✅ Production fix comments found")
        else:
            print("❌ Production fix comments not found")
            return False
            
        print("✅ PERIOD ACQUIRED FIX VERIFIED")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying period acquired fix: {e}")
        return False

def main():
    """Run verification"""
    print("🔧 VERIFYING BOTH FIXES")
    print("=" * 50)
    
    auto_learning_ok = verify_auto_learning_fix()
    period_acquired_ok = verify_period_acquired_fix()
    
    print("\n📋 VERIFICATION SUMMARY")
    print("=" * 30)
    
    if auto_learning_ok and period_acquired_ok:
        print("🎉 BOTH FIXES VERIFIED SUCCESSFULLY!")
        print("\n✅ ISSUE 1 FIXED: Auto-learning system re-enabled")
        print("   - EnhancedDictionaryAutoLearning imported and initialized")
        print("   - Auto-learning session started during extraction")
        print("   - Real-time item discovery now active")
        
        print("\n✅ ISSUE 2 FIXED: Period acquired now dynamic")
        print("   - Removed hardcoded '2025-06' values")
        print("   - Uses current payroll month/year from session")
        print("   - Tracker tables will show correct periods")
        
        return True
    else:
        print("❌ Some fixes failed verification")
        if not auto_learning_ok:
            print("   - Auto-learning fix needs attention")
        if not period_acquired_ok:
            print("   - Period acquired fix needs attention")
        return False

if __name__ == "__main__":
    main()
