#!/usr/bin/env python3
"""
Test script to verify both fixes:
1. Auto-learning system is working and detecting new items
2. Period acquired is using current payroll period instead of hardcoded dates
"""

import os
import sys
import sqlite3
from datetime import datetime

def test_auto_learning_system():
    """Test if auto-learning system is properly initialized and working"""
    print("🤖 TESTING AUTO-LEARNING SYSTEM")
    print("=" * 50)
    
    try:
        # Test 1: Import and initialize hybrid extraction integration
        sys.path.append('core')
        from hybrid_extraction_integration import HybridExtractionIntegration
        
        print("✅ Successfully imported HybridExtractionIntegration")
        
        # Test 2: Initialize with debug mode
        extractor = HybridExtractionIntegration(debug=True)
        print("✅ Successfully initialized HybridExtractionIntegration")
        
        # Test 3: Check if auto-learning is enabled
        if extractor.auto_learning is not None:
            print("✅ Auto-learning system is ENABLED")
            print(f"   Auto-learning type: {type(extractor.auto_learning)}")
            
            # Test 4: Check if session is active
            if hasattr(extractor.auto_learning, 'auto_learning_session'):
                session = extractor.auto_learning.auto_learning_session
                if session.get('active', False):
                    print(f"✅ Auto-learning session is ACTIVE")
                    print(f"   Session ID: {session.get('session_id', 'N/A')}")
                    print(f"   Session Name: {session.get('session_name', 'N/A')}")
                else:
                    print("❌ Auto-learning session is NOT active")
            else:
                print("⚠️ Auto-learning session info not available")
                
        else:
            print("❌ Auto-learning system is DISABLED (None)")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Auto-learning test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_period_acquired_fix():
    """Test if period_acquired is using dynamic dates"""
    print("\n📅 TESTING PERIOD ACQUIRED FIX")
    print("=" * 50)
    
    try:
        # Test 1: Check phased_process_manager.py for hardcoded dates
        with open('core/phased_process_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Look for hardcoded 2025-06
        hardcoded_count = content.count("'2025-06'")
        if hardcoded_count == 0:
            print("✅ No hardcoded '2025-06' found in phased_process_manager.py")
        else:
            print(f"❌ Found {hardcoded_count} hardcoded '2025-06' instances")
            
        # Look for dynamic period usage
        dynamic_patterns = [
            'f"{self.current_year}-{self.current_month:02d}"',
            'self.current_year',
            'self.current_month'
        ]
        
        dynamic_found = False
        for pattern in dynamic_patterns:
            if pattern in content:
                dynamic_found = True
                print(f"✅ Found dynamic period pattern: {pattern}")
                
        if not dynamic_found:
            print("❌ No dynamic period patterns found")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Period acquired test failed: {e}")
        return False

def test_database_period_values():
    """Test actual database values for period_acquired"""
    print("\n🗄️ TESTING DATABASE PERIOD VALUES")
    print("=" * 50)
    
    try:
        db_path = 'data/templar_payroll_auditor.db'
        if not os.path.exists(db_path):
            print("⚠️ Database not found, skipping database test")
            return True
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check tracker_results table
        try:
            cursor.execute("""
                SELECT DISTINCT period_acquired, COUNT(*) as count
                FROM tracker_results 
                WHERE period_acquired IS NOT NULL
                GROUP BY period_acquired
                ORDER BY period_acquired DESC
                LIMIT 10
            """)
            
            results = cursor.fetchall()
            if results:
                print("📊 Recent period_acquired values in tracker_results:")
                for period, count in results:
                    print(f"   {period}: {count} records")
                    
                # Check if latest period is current month/year
                latest_period = results[0][0]
                current_period = datetime.now().strftime('%Y-%m')
                
                if latest_period == current_period:
                    print(f"✅ Latest period matches current month: {latest_period}")
                else:
                    print(f"⚠️ Latest period ({latest_period}) differs from current ({current_period})")
                    print("   This might be expected if no recent processing has occurred")
            else:
                print("⚠️ No period_acquired values found in tracker_results")
                
        except sqlite3.OperationalError as e:
            print(f"⚠️ tracker_results table issue: {e}")
            
        # Check loan tracker tables
        for table in ['in_house_loans', 'external_loans']:
            try:
                cursor.execute(f"""
                    SELECT DISTINCT period_acquired, COUNT(*) as count
                    FROM {table}
                    WHERE period_acquired IS NOT NULL
                    GROUP BY period_acquired
                    ORDER BY period_acquired DESC
                    LIMIT 5
                """)
                
                results = cursor.fetchall()
                if results:
                    print(f"📊 Recent period_acquired values in {table}:")
                    for period, count in results:
                        print(f"   {period}: {count} records")
                        
            except sqlite3.OperationalError:
                print(f"⚠️ {table} table not found or has issues")
                
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 TESTING AUTO-LEARNING AND PERIOD ACQUIRED FIXES")
    print("=" * 60)
    
    results = []
    
    # Test 1: Auto-learning system
    results.append(test_auto_learning_system())
    
    # Test 2: Period acquired fix
    results.append(test_period_acquired_fix())
    
    # Test 3: Database period values
    results.append(test_database_period_values())
    
    # Summary
    print("\n📋 TEST SUMMARY")
    print("=" * 30)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ FIXES VERIFIED:")
        print("   1. Auto-learning system is now ENABLED")
        print("   2. Period acquired uses DYNAMIC dates")
        print("   3. No more hardcoded '2025-06' values")
    else:
        print("⚠️ Some tests failed - please review the output above")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
