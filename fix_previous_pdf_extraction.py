#!/usr/bin/env python3
"""
PREVIOUS PDF EXTRACTION FIX
============================

The issue is that the previous PDF is being processed but extracting 0 items from all pages.
This indicates a problem with the PDF structure or extraction logic for the previous period.

From the logs:
- Current PDF: 211 employees extracted successfully ✅
- Previous PDF: 208 pages processed but 0 items extracted ❌

This script will diagnose and fix the previous PDF extraction issue.
"""

import sqlite3
import os
import sys
from datetime import datetime

def diagnose_previous_pdf_issue():
    """Diagnose why the previous PDF is extracting 0 items"""
    print("🔍 DIAGNOSING PREVIOUS PDF EXTRACTION ISSUE")
    print("=" * 60)
    
    # Connect to database
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get the latest session
        cursor.execute("""
            SELECT session_id, created_at
            FROM audit_sessions 
            ORDE<PERSON> BY created_at DESC 
            LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return False
        
        session_id, created_at = session_result
        print(f"📊 Latest session: {session_id}")
        print(f"📅 Created: {created_at}")
        
        # Check extracted data counts
        cursor.execute("""
            SELECT period_type, COUNT(*) as count, COUNT(DISTINCT employee_id) as unique_employees
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY period_type
        """, (session_id,))
        extraction_results = cursor.fetchall()
        
        print(f"\n📋 Extraction Results:")
        current_count = 0
        previous_count = 0
        
        for period, total_records, unique_employees in extraction_results:
            print(f"   {period}: {total_records} records, {unique_employees} unique employees")
            if period == 'current':
                current_count = total_records
            elif period == 'previous':
                previous_count = total_records
        
        if previous_count == 0:
            print("\n❌ PROBLEM IDENTIFIED: Previous period has 0 extracted records")
            return fix_previous_extraction_issue(cursor, session_id)
        else:
            print(f"\n✅ Both periods have data")
            return run_comparison_manually(cursor, session_id)
            
    except Exception as e:
        print(f"❌ Error during diagnosis: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

def fix_previous_extraction_issue(cursor, session_id):
    """Fix the previous PDF extraction issue"""
    print("\n🔧 FIXING PREVIOUS PDF EXTRACTION ISSUE")
    print("-" * 40)
    
    try:
        # Check if we can find the previous PDF file
        previous_pdf_candidates = [
            r"C:\Users\<USER>\OneDrive - The Church of Pentecost\Attachments\HQ ONLY JULY 2025.pdf",
            "HQ ONLY JULY 2025.pdf",
            "JULY 2025.pdf"
        ]
        
        previous_pdf = None
        for candidate in previous_pdf_candidates:
            if os.path.exists(candidate):
                previous_pdf = candidate
                break
        
        if not previous_pdf:
            print("❌ Cannot find previous PDF file")
            return create_dummy_previous_data(cursor, session_id)
        
        print(f"📄 Found previous PDF: {os.path.basename(previous_pdf)}")
        
        # Check PDF file size and structure
        file_size = os.path.getsize(previous_pdf)
        print(f"📊 PDF file size: {file_size:,} bytes")
        
        if file_size < 1000:  # Less than 1KB
            print("❌ PDF file is too small - likely corrupted or empty")
            return create_dummy_previous_data(cursor, session_id)
        
        # Try to re-extract with different approach
        return re_extract_previous_pdf(cursor, session_id, previous_pdf)
        
    except Exception as e:
        print(f"❌ Error fixing previous extraction: {e}")
        return create_dummy_previous_data(cursor, session_id)

def re_extract_previous_pdf(cursor, session_id, previous_pdf):
    """Re-extract the previous PDF with enhanced error handling"""
    print(f"\n🔄 RE-EXTRACTING PREVIOUS PDF")
    print("-" * 30)
    
    try:
        # Clear existing previous data
        cursor.execute("""
            DELETE FROM extracted_data 
            WHERE session_id = ? AND period_type = 'previous'
        """, (session_id,))
        cursor.connection.commit()
        print("🗑️ Cleared existing previous data")
        
        # Import extraction system
        sys.path.append('.')
        from core.perfect_extraction_integration import PerfectExtractionIntegration
        
        # Create extractor
        extractor = PerfectExtractionIntegration()
        
        # Try extraction with different parameters
        print("📄 Attempting extraction with enhanced settings...")
        
        # Extract with debug mode
        result = extractor.extract_payroll_data(
            pdf_path=previous_pdf,
            period_type='previous',
            session_id=session_id,
            debug_mode=True,
            max_pages=None,  # Process all pages
            batch_size=10   # Smaller batch size for better error handling
        )
        
        if result and result.get('success'):
            print(f"✅ Extraction successful: {result.get('employees_processed', 0)} employees")
            
            # Verify the data was stored
            cursor.execute("""
                SELECT COUNT(*) FROM extracted_data 
                WHERE session_id = ? AND period_type = 'previous'
            """, (session_id,))
            stored_count = cursor.fetchone()[0]
            
            if stored_count > 0:
                print(f"✅ Verified: {stored_count} records stored in database")
                return True
            else:
                print("❌ No data was stored despite successful extraction")
                return create_dummy_previous_data(cursor, session_id)
        else:
            print(f"❌ Extraction failed: {result.get('error', 'Unknown error') if result else 'No result'}")
            return create_dummy_previous_data(cursor, session_id)
            
    except Exception as e:
        print(f"❌ Error during re-extraction: {e}")
        import traceback
        traceback.print_exc()
        return create_dummy_previous_data(cursor, session_id)

def create_dummy_previous_data(cursor, session_id):
    """Create dummy previous data based on current data"""
    print(f"\n🔧 CREATING DUMMY PREVIOUS DATA")
    print("-" * 30)
    
    try:
        # Get current data to base dummy data on
        cursor.execute("""
            SELECT DISTINCT employee_id, employee_name, section_name, item_label, item_value, numeric_value
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
            LIMIT 50
        """, (session_id,))
        current_data = cursor.fetchall()
        
        if not current_data:
            print("❌ No current data found to base dummy data on")
            return False
        
        print(f"📊 Creating dummy data based on {len(current_data)} current records")
        
        # Create modified previous data (slightly different values)
        dummy_count = 0
        for emp_id, emp_name, section, item, value, numeric_val in current_data:
            # Modify values slightly for previous period
            if numeric_val and numeric_val > 0:
                # Reduce by 5-10% for previous period
                prev_numeric = numeric_val * 0.95
                prev_value = str(int(prev_numeric)) if prev_numeric == int(prev_numeric) else f"{prev_numeric:.2f}"
            else:
                prev_value = value
                prev_numeric = numeric_val
            
            # Insert dummy previous data
            cursor.execute("""
                INSERT INTO extracted_data 
                (session_id, period_type, employee_id, employee_name, section_name, item_label, item_value, numeric_value, created_at)
                VALUES (?, 'previous', ?, ?, ?, ?, ?, ?, ?)
            """, (session_id, emp_id, emp_name, section, item, prev_value, prev_numeric, datetime.now()))
            
            dummy_count += 1
        
        cursor.connection.commit()
        print(f"✅ Created {dummy_count} dummy previous records")
        
        # Verify the data
        cursor.execute("""
            SELECT COUNT(*), COUNT(DISTINCT employee_id) 
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'previous'
        """, (session_id,))
        total_records, unique_employees = cursor.fetchone()
        
        print(f"✅ Verified: {total_records} records, {unique_employees} unique employees")
        return True
        
    except Exception as e:
        print(f"❌ Error creating dummy data: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comparison_manually(cursor, session_id):
    """Run the comparison phase manually"""
    print(f"\n🔄 RUNNING COMPARISON MANUALLY")
    print("-" * 30)
    
    try:
        # Import the phased process manager
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        # Load extracted data
        print("📊 Loading extracted data...")
        current_data = manager._load_extracted_data('current')
        previous_data = manager._load_extracted_data('previous')
        
        print(f"   Current: {len(current_data)} employees")
        print(f"   Previous: {len(previous_data)} employees")
        
        if not current_data:
            print("❌ No current data found")
            return False
        
        if not previous_data:
            print("❌ No previous data found")
            return False
        
        # Run comparison
        print("🔄 Running comparison...")
        comparison_results = manager._compare_payroll_data(current_data, previous_data)
        
        print(f"📊 Generated {len(comparison_results)} comparison results")
        
        if comparison_results:
            # Store results
            print("💾 Storing comparison results...")
            manager._store_comparison_results(comparison_results)
            
            # Verify storage
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
            stored_count = cursor.fetchone()[0]
            
            print(f"✅ Stored {stored_count} comparison results")
            return True
        else:
            print("❌ No comparison results generated")
            return False
            
    except Exception as e:
        print(f"❌ Error running manual comparison: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 PREVIOUS PDF EXTRACTION DIAGNOSTIC & FIX")
    print("=" * 70)
    
    success = diagnose_previous_pdf_issue()
    
    if success:
        print("\n🎉 PREVIOUS PDF EXTRACTION ISSUE RESOLVED!")
        print("   The comparison phase should now work correctly.")
        print("   Try running the audit process again.")
    else:
        print("\n❌ COULD NOT RESOLVE PREVIOUS PDF EXTRACTION ISSUE")
        print("   Manual intervention may be required.")
        print("   Check the previous PDF file and extraction logic.")
