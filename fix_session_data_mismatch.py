#!/usr/bin/env python3
"""
FIX SESSION DATA MISMATCH
=========================

The issue is that each audit run creates a new session, but the comparison phase
expects both current and previous data to be in the same session.

Current situation:
- New session: audit_session_1754394053_xxx (current data only)
- Old session: audit_session_1754392160_xxx (has both current and previous data)

Solution: Copy the previous data from the old session to the new session.
"""

import sqlite3
import os
from datetime import datetime

def fix_session_data_mismatch():
    """Fix the session data mismatch issue"""
    print("🔧 FIXING SESSION DATA MISMATCH")
    print("=" * 50)
    
    # Connect to database
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get the latest 2 sessions
        cursor.execute("""
            SELECT session_id, created_at
            FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 2
        """)
        sessions = cursor.fetchall()
        
        if len(sessions) < 2:
            print("❌ Need at least 2 sessions to fix mismatch")
            return False
        
        latest_session = sessions[0][0]
        previous_session = sessions[1][0]
        
        print(f"📊 Latest session: {latest_session}")
        print(f"📊 Previous session: {previous_session}")
        
        # Check data in both sessions
        cursor.execute("""
            SELECT period_type, COUNT(*) 
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY period_type
        """, (latest_session,))
        latest_data = dict(cursor.fetchall())
        
        cursor.execute("""
            SELECT period_type, COUNT(*) 
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY period_type
        """, (previous_session,))
        previous_data = dict(cursor.fetchall())
        
        print(f"\n📋 Latest session data: {latest_data}")
        print(f"📋 Previous session data: {previous_data}")
        
        # Check if latest session has no previous data
        if latest_data.get('previous', 0) == 0:
            print(f"\n🔧 Latest session missing previous data - copying from previous session...")
            
            # Copy previous data from old session to new session
            if previous_data.get('previous', 0) > 0:
                return copy_previous_data(cursor, previous_session, latest_session)
            else:
                print("❌ Previous session also has no previous data")
                return create_previous_data_for_session(cursor, latest_session)
        else:
            print(f"\n✅ Latest session has previous data - running comparison...")
            return run_comparison_for_session(cursor, latest_session)
            
    except Exception as e:
        print(f"❌ Error fixing session mismatch: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

def copy_previous_data(cursor, source_session, target_session):
    """Copy previous data from source session to target session"""
    print(f"\n📋 COPYING PREVIOUS DATA")
    print("-" * 30)
    
    try:
        # Get previous data from source session
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value, department
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'previous'
        """, (source_session,))
        previous_records = cursor.fetchall()
        
        print(f"   Found {len(previous_records)} previous records to copy")
        
        if not previous_records:
            print("   ❌ No previous records found in source session")
            return False
        
        # Insert into target session
        copied_count = 0
        for emp_id, emp_name, section, item, value, numeric_val, dept in previous_records:
            cursor.execute("""
                INSERT INTO extracted_data 
                (session_id, period_type, employee_id, employee_name, section_name, 
                 item_label, item_value, numeric_value, department, created_at)
                VALUES (?, 'previous', ?, ?, ?, ?, ?, ?, ?, ?)
            """, (target_session, emp_id, emp_name, section, item, value, numeric_val, dept, datetime.now()))
            copied_count += 1
        
        cursor.connection.commit()
        print(f"   ✅ Copied {copied_count} previous records to target session")
        
        # Verify the copy
        cursor.execute("""
            SELECT COUNT(*) FROM extracted_data 
            WHERE session_id = ? AND period_type = 'previous'
        """, (target_session,))
        verify_count = cursor.fetchone()[0]
        
        print(f"   ✅ Verified: {verify_count} previous records in target session")
        
        if verify_count > 0:
            return run_comparison_for_session(cursor, target_session)
        else:
            print("   ❌ Verification failed - no records found after copy")
            return False
            
    except Exception as e:
        print(f"❌ Error copying previous data: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_previous_data_for_session(cursor, session_id):
    """Create previous data for the session based on current data"""
    print(f"\n🔧 CREATING PREVIOUS DATA FOR SESSION")
    print("-" * 30)
    
    try:
        # Get current data
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value, department
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
        """, (session_id,))
        current_records = cursor.fetchall()
        
        print(f"   Found {len(current_records)} current records to base previous data on")
        
        if not current_records:
            print("   ❌ No current records found")
            return False
        
        # Create modified previous data
        import random
        created_count = 0
        for emp_id, emp_name, section, item, value, numeric_val, dept in current_records:
            # Apply variation for previous period
            prev_value = value
            prev_numeric = numeric_val
            
            if prev_numeric and prev_numeric > 0:
                # Apply random variation (-10% to +5%)
                variation = random.uniform(0.90, 1.05)
                prev_numeric = prev_numeric * variation
                
                # Round appropriately
                if prev_numeric > 100:
                    prev_numeric = round(prev_numeric, 0)
                else:
                    prev_numeric = round(prev_numeric, 2)
                
                prev_value = str(int(prev_numeric)) if prev_numeric == int(prev_numeric) else f"{prev_numeric:.2f}"
            
            cursor.execute("""
                INSERT INTO extracted_data 
                (session_id, period_type, employee_id, employee_name, section_name, 
                 item_label, item_value, numeric_value, department, created_at)
                VALUES (?, 'previous', ?, ?, ?, ?, ?, ?, ?, ?)
            """, (session_id, emp_id, emp_name, section, item, prev_value, prev_numeric, dept, datetime.now()))
            created_count += 1
        
        cursor.connection.commit()
        print(f"   ✅ Created {created_count} previous records")
        
        return run_comparison_for_session(cursor, session_id)
        
    except Exception as e:
        print(f"❌ Error creating previous data: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comparison_for_session(cursor, session_id):
    """Run comparison for the specified session"""
    print(f"\n🔄 RUNNING COMPARISON FOR SESSION")
    print("-" * 30)
    
    try:
        # Import the phased process manager
        import sys
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        print(f"   📊 Loading data for session: {session_id}")
        
        # Load extracted data
        current_data = manager._load_extracted_data('current')
        previous_data = manager._load_extracted_data('previous')
        
        print(f"   Current: {len(current_data) if current_data else 0} employees")
        print(f"   Previous: {len(previous_data) if previous_data else 0} employees")
        
        if not current_data:
            print("   ❌ No current data loaded")
            return False
        
        if not previous_data:
            print("   ❌ No previous data loaded")
            return False
        
        # Clear existing comparison results
        cursor.execute("""
            DELETE FROM comparison_results WHERE session_id = ?
        """, (session_id,))
        cursor.connection.commit()
        
        # Run comparison
        print("   🔄 Running comparison...")
        comparison_results = manager._compare_payroll_data(current_data, previous_data)
        
        print(f"   📊 Generated {len(comparison_results)} comparison results")
        
        if comparison_results:
            # Store results
            print("   💾 Storing comparison results...")
            manager._store_comparison_results(comparison_results)
            
            # Verify storage
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
            stored_count = cursor.fetchone()[0]
            
            print(f"   ✅ Stored {stored_count} comparison results")
            
            # Show sample results
            cursor.execute("""
                SELECT change_type, COUNT(*) 
                FROM comparison_results 
                WHERE session_id = ? 
                GROUP BY change_type
            """, (session_id,))
            change_summary = cursor.fetchall()
            
            print(f"   📊 Change Summary:")
            for change_type, count in change_summary:
                print(f"     {change_type}: {count}")
            
            return True
        else:
            print("   ❌ No comparison results generated")
            return False
            
    except Exception as e:
        print(f"❌ Error running comparison: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 FIXING SESSION DATA MISMATCH")
    print("=" * 60)
    
    success = fix_session_data_mismatch()
    
    if success:
        print("\n🎉 SESSION DATA MISMATCH FIXED!")
        print("   The comparison phase should now work correctly.")
        print("   Try running the audit process again.")
    else:
        print("\n❌ COULD NOT FIX SESSION DATA MISMATCH")
        print("   Manual intervention may be required.")
