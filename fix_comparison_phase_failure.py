#!/usr/bin/env python3
"""
COMPARISON PHASE FAILURE FIX
============================

This script diagnoses and fixes the COMPARISON phase failure in the database-only workflow.
The error indicates that the comparison phase is failing, likely due to missing extracted data.

Error: "Database-only process failed: COMPARISON phase failed"
"""

import sqlite3
import os
import sys
import json
from datetime import datetime

def diagnose_comparison_failure():
    """Diagnose why the comparison phase is failing"""
    print("🔍 DIAGNOSING COMPARISON PHASE FAILURE")
    print("=" * 50)
    
    # Connect to database
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check for recent sessions
        cursor.execute("""
            SELECT session_id, current_month, current_year, created_at
            FROM audit_sessions
            ORDER BY created_at DESC
            LIMIT 5
        """)
        sessions = cursor.fetchall()

        if not sessions:
            print("❌ No audit sessions found in database")
            return False

        print(f"📊 Found {len(sessions)} recent sessions:")
        for session in sessions:
            session_id, month, year, created = session
            print(f"   {session_id}: {month} {year} - {created}")
        
        # Get the most recent session
        latest_session = sessions[0][0]
        print(f"\n🎯 Analyzing latest session: {latest_session}")
        
        # Check extracted data
        cursor.execute("""
            SELECT period_type, COUNT(*) as count
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY period_type
        """, (latest_session,))
        extracted_counts = cursor.fetchall()
        
        print(f"\n📋 Extracted Data Status:")
        if extracted_counts:
            for period, count in extracted_counts:
                print(f"   {period}: {count} records")
        else:
            print("   ❌ No extracted data found!")
            return fix_missing_extracted_data(cursor, latest_session)
        
        # Check if both current and previous data exist
        current_count = 0
        previous_count = 0
        for period, count in extracted_counts:
            if period == 'current':
                current_count = count
            elif period == 'previous':
                previous_count = count
        
        if current_count == 0:
            print("   ❌ No current period data found!")
            return fix_missing_extracted_data(cursor, latest_session)
        
        if previous_count == 0:
            print("   ❌ No previous period data found!")
            return fix_missing_extracted_data(cursor, latest_session)
        
        print(f"   ✅ Both periods have data: current={current_count}, previous={previous_count}")
        
        # Check comparison results
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results WHERE session_id = ?
        """, (latest_session,))
        comparison_count = cursor.fetchone()[0]
        
        print(f"\n📊 Comparison Results: {comparison_count} records")
        
        if comparison_count == 0:
            print("   ⚠️ No comparison results - running comparison manually...")
            return run_manual_comparison(cursor, latest_session)
        else:
            print("   ✅ Comparison results exist")
            return True
            
    except Exception as e:
        print(f"❌ Error during diagnosis: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

def fix_missing_extracted_data(cursor, session_id):
    """Fix missing extracted data by re-running extraction"""
    print("\n🔧 FIXING MISSING EXTRACTED DATA")
    print("-" * 30)
    
    try:
        # Check if we have session files to re-extract from
        session_dir = f"temp_sessions/{session_id}"
        if os.path.exists(session_dir):
            print(f"   📁 Found session directory: {session_dir}")
            
            # Look for PDF files
            current_pdf = None
            previous_pdf = None
            
            for file in os.listdir(session_dir):
                if 'current' in file.lower() and file.endswith('.pdf'):
                    current_pdf = os.path.join(session_dir, file)
                elif 'previous' in file.lower() and file.endswith('.pdf'):
                    previous_pdf = os.path.join(session_dir, file)
            
            if current_pdf and previous_pdf:
                print(f"   📄 Found PDFs: {os.path.basename(current_pdf)}, {os.path.basename(previous_pdf)}")
                return re_extract_data(session_id, current_pdf, previous_pdf)
            else:
                print("   ❌ PDF files not found in session directory")
        else:
            print("   ❌ Session directory not found")
        
        # Try to find any recent PDF files in common locations
        return find_and_extract_recent_pdfs(session_id)
        
    except Exception as e:
        print(f"   ❌ Error fixing extracted data: {e}")
        return False

def run_manual_comparison(cursor, session_id):
    """Run comparison manually using existing extracted data"""
    print("\n🔧 RUNNING MANUAL COMPARISON")
    print("-" * 30)
    
    try:
        # Import the phased process manager
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        # Load extracted data
        print("   📊 Loading extracted data...")
        current_data = manager._load_extracted_data('current')
        previous_data = manager._load_extracted_data('previous')
        
        print(f"   Current: {len(current_data)} employees")
        print(f"   Previous: {len(previous_data)} employees")
        
        if not current_data or not previous_data:
            print("   ❌ Cannot load extracted data")
            return False
        
        # Run comparison
        print("   🔄 Running comparison...")
        comparison_results = manager._compare_payroll_data(current_data, previous_data)
        
        print(f"   📊 Generated {len(comparison_results)} comparison results")
        
        if comparison_results:
            # Store results
            print("   💾 Storing comparison results...")
            manager._store_comparison_results(comparison_results)
            
            # Verify storage
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
            stored_count = cursor.fetchone()[0]
            
            print(f"   ✅ Stored {stored_count} comparison results")
            
            # Update session with comparison completion
            cursor.execute("""
                UPDATE audit_sessions
                SET updated_at = ?
                WHERE session_id = ?
            """, (datetime.now().isoformat(), session_id))
            cursor.connection.commit()

            print("   ✅ Updated session timestamp")
            return True
        else:
            print("   ❌ No comparison results generated")
            return False
            
    except Exception as e:
        print(f"   ❌ Error running manual comparison: {e}")
        import traceback
        traceback.print_exc()
        return False

def re_extract_data(session_id, current_pdf, previous_pdf):
    """Re-extract data from PDF files"""
    print(f"\n🔄 RE-EXTRACTING DATA")
    print("-" * 30)
    
    try:
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        # Re-run extraction phase
        print("   📄 Re-running extraction phase...")
        options = {
            'current_pdf': current_pdf,
            'previous_pdf': previous_pdf,
            'use_perfect_extractor': True
        }
        
        success = manager._phase_extraction(options)
        
        if success:
            print("   ✅ Extraction completed successfully")
            return True
        else:
            print("   ❌ Extraction failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error re-extracting data: {e}")
        import traceback
        traceback.print_exc()
        return False

def find_and_extract_recent_pdfs(session_id):
    """Find recent PDF files and extract data"""
    print("\n🔍 SEARCHING FOR RECENT PDF FILES")
    print("-" * 30)
    
    # Common locations to search
    search_paths = [
        ".",
        "temp_sessions",
        "data",
        os.path.expanduser("~/Downloads"),
        os.path.expanduser("~/Desktop")
    ]
    
    pdf_files = []
    for search_path in search_paths:
        if os.path.exists(search_path):
            for root, dirs, files in os.walk(search_path):
                for file in files:
                    if file.endswith('.pdf') and any(keyword in file.lower() for keyword in ['payroll', 'salary', 'pay']):
                        pdf_path = os.path.join(root, file)
                        pdf_files.append((pdf_path, os.path.getmtime(pdf_path)))
    
    if pdf_files:
        # Sort by modification time (newest first)
        pdf_files.sort(key=lambda x: x[1], reverse=True)
        
        print(f"   📄 Found {len(pdf_files)} potential PDF files:")
        for i, (pdf_path, mtime) in enumerate(pdf_files[:5]):  # Show top 5
            mod_time = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M')
            print(f"   {i+1}. {os.path.basename(pdf_path)} ({mod_time})")
        
        if len(pdf_files) >= 2:
            current_pdf = pdf_files[0][0]  # Most recent
            previous_pdf = pdf_files[1][0]  # Second most recent
            
            print(f"\n   🎯 Using:")
            print(f"   Current: {os.path.basename(current_pdf)}")
            print(f"   Previous: {os.path.basename(previous_pdf)}")
            
            return re_extract_data(session_id, current_pdf, previous_pdf)
    
    print("   ❌ No suitable PDF files found")
    return False

if __name__ == "__main__":
    print("🚀 COMPARISON PHASE FAILURE DIAGNOSTIC & FIX")
    print("=" * 60)
    
    success = diagnose_comparison_failure()
    
    if success:
        print("\n🎉 COMPARISON PHASE ISSUE RESOLVED!")
        print("   The system should now work correctly.")
        print("   Try running the audit process again.")
    else:
        print("\n❌ COULD NOT RESOLVE COMPARISON PHASE ISSUE")
        print("   Manual intervention may be required.")
        print("   Check the database and PDF files manually.")
