#!/usr/bin/env python3
"""
Final comprehensive test to verify ALL Dictionary Manager operations properly hit the database.
This ensures we don't have incidents where the database is not respected.
"""

import sqlite3
import os
import json
import subprocess
import sys

def run_command(command_args):
    """Run a dictionary manager command and return the result"""
    try:
        result = subprocess.run(
            ['python', 'core/dictionary_manager.py'] + command_args,
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, '', str(e)

def test_all_database_operations():
    """Test all critical dictionary database operations"""
    
    print('=== FINAL COMPREHENSIVE DATABASE OPERATIONS TEST ===\n')
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print('❌ Database file not found')
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get initial state
    cursor.execute('SELECT COUNT(*) FROM dictionary_items')
    initial_count = cursor.fetchone()[0]
    print(f'Initial items in database: {initial_count}')
    
    all_tests_passed = True
    
    # Test 1: Toggle include_in_report
    print('\n1. 🔄 TESTING INCLUDE_IN_REPORT TOGGLE:')
    
    # Find a test item
    cursor.execute('''
        SELECT ds.section_name, di.item_name, di.include_in_report
        FROM dictionary_items di 
        JOIN dictionary_sections ds ON di.section_id = ds.id 
        WHERE di.item_name = 'LEAVE ALLOWANCE'
        LIMIT 1
    ''')
    test_item = cursor.fetchone()
    
    if test_item:
        section, item, current_value = test_item
        new_value = not current_value
        
        # Test the command
        success, output, error = run_command([
            'set-include-in-report', section, item, str(new_value).lower()
        ])
        
        if success and output == 'true':
            # Verify in database
            cursor.execute('''
                SELECT di.include_in_report
                FROM dictionary_items di 
                JOIN dictionary_sections ds ON di.section_id = ds.id 
                WHERE ds.section_name = ? AND di.item_name = ?
            ''', (section, item))
            
            db_value = cursor.fetchone()[0]
            if db_value == (1 if new_value else 0):
                print(f'   ✅ Toggle worked: {section}.{item} = {new_value}')
                
                # Restore original value
                run_command(['set-include-in-report', section, item, str(current_value).lower()])
            else:
                print(f'   ❌ Database not updated: expected {new_value}, got {db_value}')
                all_tests_passed = False
        else:
            print(f'   ❌ Command failed: {error}')
            all_tests_passed = False
    else:
        print('   ⚠️  No test item found')
    
    # Test 2: Toggle change type inclusion
    print('\n2. 🔄 TESTING CHANGE TYPE INCLUSION TOGGLE:')
    
    if test_item:
        section, item, _ = test_item
        
        # Test NEW change type toggle
        success, output, error = run_command([
            'set-change-type-inclusion', section, item, 'NEW', 'false'
        ])
        
        if success and output == 'true':
            # Verify in database
            cursor.execute('''
                SELECT di.include_new
                FROM dictionary_items di 
                JOIN dictionary_sections ds ON di.section_id = ds.id 
                WHERE ds.section_name = ? AND di.item_name = ?
            ''', (section, item))
            
            db_value = cursor.fetchone()[0]
            if db_value == 0:
                print(f'   ✅ Change type toggle worked: {section}.{item}.include_new = false')
                
                # Restore to true
                run_command(['set-change-type-inclusion', section, item, 'NEW', 'true'])
            else:
                print(f'   ❌ Database not updated for change type')
                all_tests_passed = False
        else:
            print(f'   ❌ Change type command failed: {error}')
            all_tests_passed = False
    
    # Test 3: Add item
    print('\n3. ➕ TESTING ADD ITEM:')
    
    test_item_name = 'TEST_DATABASE_ITEM'
    test_item_data = {
        'format': 'text',
        'value_format': 'numeric',
        'include_in_report': True,
        'standardized_name': 'test_database_item'
    }
    
    success, output, error = run_command([
        'add-item', 'EARNINGS', test_item_name, json.dumps(test_item_data)
    ])
    
    if success and output == 'true':
        # Verify in database
        cursor.execute('''
            SELECT COUNT(*)
            FROM dictionary_items di 
            JOIN dictionary_sections ds ON di.section_id = ds.id 
            WHERE ds.section_name = 'EARNINGS' AND di.item_name = ?
        ''', (test_item_name,))
        
        if cursor.fetchone()[0] > 0:
            print(f'   ✅ Add item worked: {test_item_name} added to database')
            
            # Test 4: Remove item
            print('\n4. ➖ TESTING REMOVE ITEM:')
            
            success, output, error = run_command([
                'remove-item', 'EARNINGS', test_item_name
            ])
            
            if success and output == 'true':
                # Verify removed from database
                cursor.execute('''
                    SELECT COUNT(*)
                    FROM dictionary_items di 
                    JOIN dictionary_sections ds ON di.section_id = ds.id 
                    WHERE ds.section_name = 'EARNINGS' AND di.item_name = ?
                ''', (test_item_name,))
                
                if cursor.fetchone()[0] == 0:
                    print(f'   ✅ Remove item worked: {test_item_name} removed from database')
                else:
                    print(f'   ❌ Item not removed from database')
                    all_tests_passed = False
            else:
                print(f'   ❌ Remove command failed: {error}')
                all_tests_passed = False
        else:
            print(f'   ❌ Item not added to database')
            all_tests_passed = False
    else:
        print(f'   ❌ Add command failed: {error}')
        all_tests_passed = False
    
    # Test 5: Verify final state
    print('\n5. 🔍 VERIFYING FINAL STATE:')
    
    cursor.execute('SELECT COUNT(*) FROM dictionary_items')
    final_count = cursor.fetchone()[0]
    
    if final_count == initial_count:
        print(f'   ✅ Database integrity maintained: {final_count} items')
    else:
        print(f'   ⚠️  Item count changed: {initial_count} → {final_count}')
    
    conn.close()
    
    # Summary
    print('\n6. 📋 FINAL SUMMARY:')
    if all_tests_passed:
        print('   ✅ ALL DATABASE OPERATIONS WORKING CORRECTLY')
        print('   ✅ No incidents where database is not respected')
        print('   ✅ Dictionary Manager is fully database-integrated')
        return True
    else:
        print('   ❌ SOME DATABASE OPERATIONS FAILED')
        print('   ❌ Risk of incidents where database is not respected')
        return False

if __name__ == '__main__':
    success = test_all_database_operations()
    sys.exit(0 if success else 1)
