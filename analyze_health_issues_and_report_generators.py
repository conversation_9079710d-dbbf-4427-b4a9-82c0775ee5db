#!/usr/bin/env python3
"""
Analyze health issues preventing 100% score and identify all report generators
"""

import sqlite3
import os
import glob
import subprocess
import sys

def analyze_health_issues():
    """Analyze specific issues preventing 100% health score"""
    
    print('🔍 DETAILED HEALTH ISSUE ANALYSIS')
    print('=' * 50)
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print('❌ Database not found')
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Health scoring breakdown (from production_monitoring_dashboard.py)
    print('📊 HEALTH SCORE BREAKDOWN:')
    print('-' * 30)
    
    health_score = 0
    issues = []
    
    # 1. Recent Sessions (20 points)
    cursor.execute('SELECT COUNT(*) FROM audit_sessions WHERE created_at > datetime("now", "-7 days")')
    recent_sessions = cursor.fetchone()[0]
    
    if recent_sessions > 0:
        health_score += 20
        print(f'✅ Recent Sessions: +20 points ({recent_sessions} sessions in last 7 days)')
    else:
        print(f'❌ Recent Sessions: +0 points (no sessions in last 7 days)')
        issues.append('No recent audit sessions - system may not be actively used')
    
    # 2. Configuration Conflicts (30 points)
    cursor.execute('''
        SELECT COUNT(*)
        FROM dictionary_items di
        JOIN dictionary_sections ds ON di.section_id = ds.id
        WHERE (
            (di.include_in_report = 0 AND (di.include_new = 1 OR di.include_increase = 1 OR 
             di.include_decrease = 1 OR di.include_removed = 1 OR di.include_no_change = 1))
            OR
            (di.include_in_report = 1 AND di.include_new = 0 AND di.include_increase = 0 AND 
             di.include_decrease = 0 AND di.include_removed = 0 AND di.include_no_change = 0)
        )
    ''')
    
    conflicts = cursor.fetchone()[0]
    
    if conflicts == 0:
        health_score += 30
        print(f'✅ Configuration Conflicts: +30 points (no conflicts)')
    else:
        print(f'⚠️  Configuration Conflicts: +0 points ({conflicts} conflicts found)')
        issues.append(f'{conflicts} toggle configuration conflicts need resolution')
    
    # 3. Filtering Effectiveness (30 points)
    cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    
    if session_result:
        session_id = session_result[0]
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
        total_results = cursor.fetchone()[0]
        
        cursor.execute('''
            SELECT COUNT(*)
            FROM comparison_results cr
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            LEFT JOIN dictionary_sections ds ON di.section_id = ds.id AND cr.section_name = ds.section_name
            WHERE cr.session_id = ?
            AND (
                (di.include_in_report = 1 OR di.include_in_report IS NULL)
                AND
                (
                    (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                    (cr.change_type = 'INCREASED' AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                    (cr.change_type = 'DECREASED' AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                    (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                    (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1 OR di.include_no_change IS NULL)) OR
                    (cr.change_type NOT IN ('NEW', 'INCREASED', 'DECREASED', 'REMOVED', 'NO_CHANGE'))
                )
            )
        ''', (session_id,))
        
        filtered_results = cursor.fetchone()[0]
        reduction_percentage = ((total_results - filtered_results) / total_results * 100) if total_results > 0 else 0
        
        if reduction_percentage >= 30:  # High effectiveness
            health_score += 30
            print(f'✅ Filtering Effectiveness: +30 points ({reduction_percentage:.1f}% reduction - High)')
        elif reduction_percentage >= 10:  # Medium effectiveness
            health_score += 20
            print(f'⚠️  Filtering Effectiveness: +20 points ({reduction_percentage:.1f}% reduction - Medium)')
            issues.append('Filtering effectiveness could be improved')
        else:
            print(f'❌ Filtering Effectiveness: +0 points ({reduction_percentage:.1f}% reduction - Low)')
            issues.append('Low filtering effectiveness - review dictionary configuration')
    
    # 4. Recent Reports (20 points)
    cursor.execute('SELECT COUNT(*) FROM reports WHERE created_at > datetime("now", "-7 days")')
    recent_reports = cursor.fetchone()[0]
    
    if recent_reports > 0:
        health_score += 20
        print(f'✅ Recent Reports: +20 points ({recent_reports} reports in last 7 days)')
    else:
        print(f'⚠️  Recent Reports: +0 points (no reports in last 7 days)')
        issues.append('No recent reports generated - verify report generation is working')
    
    print(f'\n🏥 TOTAL HEALTH SCORE: {health_score}/100')
    
    print(f'\n🚨 ISSUES PREVENTING 100% SCORE:')
    if issues:
        for i, issue in enumerate(issues, 1):
            print(f'{i}. {issue}')
    else:
        print('✅ No issues found - system is at optimal health')
    
    conn.close()
    return health_score, issues

def identify_all_report_generators():
    """Identify all report generators in the system"""
    
    print('\n📄 ALL REPORT GENERATORS IN THE SYSTEM')
    print('=' * 50)
    
    # Find all Python files that might be report generators
    report_patterns = [
        '*report*.py',
        '*Report*.py',
        'generate_*.py',
        'core/*report*.py',
        'core/*Report*.py'
    ]
    
    report_files = []
    for pattern in report_patterns:
        report_files.extend(glob.glob(pattern))
    
    # Remove duplicates and sort
    report_files = sorted(list(set(report_files)))
    
    print(f'🔍 Found {len(report_files)} potential report generator files:')
    
    active_generators = []
    inactive_generators = []
    
    for report_file in report_files:
        print(f'\n📋 Analyzing: {report_file}')
        
        # Check if file exists and is readable
        if not os.path.exists(report_file):
            print(f'   ❌ File not found')
            continue
        
        # Try to determine if it's a report generator by checking content
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for report generation indicators
            indicators = [
                'def generate',
                'report',
                'comparison_results',
                'session_id',
                'Word',
                'docx',
                'html'
            ]
            
            indicator_count = sum(1 for indicator in indicators if indicator.lower() in content.lower())
            
            if indicator_count >= 3:
                print(f'   ✅ Likely report generator ({indicator_count}/8 indicators)')
                
                # Test if it can be executed
                try:
                    # Try to run with --help or similar to see if it's executable
                    result = subprocess.run(
                        [sys.executable, report_file, '--help'],
                        capture_output=True,
                        text=True,
                        timeout=5
                    )
                    
                    if result.returncode == 0 or 'usage' in result.stdout.lower() or 'help' in result.stdout.lower():
                        print(f'   ✅ Executable with help')
                        active_generators.append(report_file)
                    else:
                        # Try without --help
                        result = subprocess.run(
                            [sys.executable, report_file],
                            capture_output=True,
                            text=True,
                            timeout=5
                        )
                        
                        if 'usage' in result.stdout.lower() or 'command' in result.stdout.lower():
                            print(f'   ✅ Executable (shows usage)')
                            active_generators.append(report_file)
                        elif result.returncode != 0 and 'error' not in result.stderr.lower():
                            print(f'   ⚠️  May be executable but needs parameters')
                            active_generators.append(report_file)
                        else:
                            print(f'   ❌ Not executable or has errors')
                            inactive_generators.append(report_file)
                
                except subprocess.TimeoutExpired:
                    print(f'   ⚠️  Execution timeout - may be waiting for input')
                    active_generators.append(report_file)
                except Exception as e:
                    print(f'   ❌ Execution error: {str(e)[:50]}')
                    inactive_generators.append(report_file)
            else:
                print(f'   ❌ Not a report generator ({indicator_count}/8 indicators)')
                inactive_generators.append(report_file)
                
        except Exception as e:
            print(f'   ❌ Error reading file: {str(e)[:50]}')
            inactive_generators.append(report_file)
    
    print(f'\n📊 REPORT GENERATOR SUMMARY:')
    print(f'✅ Active Report Generators: {len(active_generators)}')
    for generator in active_generators:
        print(f'   • {generator}')
    
    print(f'\n❌ Inactive/Non-Report Files: {len(inactive_generators)}')
    for generator in inactive_generators:
        print(f'   • {generator}')
    
    return active_generators, inactive_generators

def main():
    """Main analysis function"""
    health_score, issues = analyze_health_issues()
    active_generators, inactive_generators = identify_all_report_generators()
    
    print(f'\n🎯 SUMMARY:')
    print(f'Health Score: {health_score}/100')
    print(f'Issues: {len(issues)}')
    print(f'Active Report Generators: {len(active_generators)}')
    print(f'Inactive Files: {len(inactive_generators)}')

if __name__ == "__main__":
    main()
