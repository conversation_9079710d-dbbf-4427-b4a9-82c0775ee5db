#!/usr/bin/env python3
"""
Test if the backend fix is working properly
"""

import sys
import json
sys.path.append('core')

def test_backend_parsing():
    """Test if the backend is properly parsing consolidated_details"""
    print("🔧 TESTING BACKEND CONSOLIDATED DETAILS PARSING")
    print("=" * 60)
    
    try:
        from phased_process_manager import PhasedProcessManager
        
        # Initialize manager
        manager = PhasedProcessManager(debug_mode=True)
        print("✅ PhasedProcessManager initialized")
        
        # Get latest comparison results
        result = manager.get_latest_comparison_results()
        
        if result and result.get('success'):
            data = result.get('data', [])
            print(f"✅ Retrieved {len(data)} comparison results")
            
            # Look for consolidated entries
            consolidated_entries = [item for item in data if item.get('change_type') == 'CONSOLIDATED']
            print(f"📊 Found {len(consolidated_entries)} consolidated entries")
            
            if consolidated_entries:
                for i, entry in enumerate(consolidated_entries[:3], 1):
                    print(f"\n🔍 Consolidated Entry {i}:")
                    print(f"   Employee: {entry.get('employee_id')}")
                    print(f"   Item: {entry.get('item_label')}")
                    
                    consolidated_details = entry.get('consolidated_details')
                    print(f"   Type: {type(consolidated_details)}")
                    
                    if isinstance(consolidated_details, list):
                        print(f"   ✅ SUCCESS: Is array with {len(consolidated_details)} items")
                        if consolidated_details:
                            print(f"   Sample item: {consolidated_details[0]}")
                    elif isinstance(consolidated_details, str):
                        print(f"   ❌ PROBLEM: Still a string")
                        print(f"   Content: {consolidated_details[:100]}...")
                        
                        # Try to parse manually
                        try:
                            parsed = json.loads(consolidated_details)
                            print(f"   Manual parse: {type(parsed)} with {len(parsed) if isinstance(parsed, list) else 'N/A'} items")
                        except:
                            print(f"   Manual parse: FAILED")
                    else:
                        print(f"   ⚠️ UNEXPECTED: {consolidated_details}")
                        
                return len(consolidated_entries) > 0
            else:
                print("⚠️ No consolidated entries found")
                return True
        else:
            print(f"❌ Failed to get comparison results: {result.get('error') if result else 'No result'}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_backend_parsing()
    if success:
        print("\n🎉 Backend test completed")
    else:
        print("\n❌ Backend test failed")
