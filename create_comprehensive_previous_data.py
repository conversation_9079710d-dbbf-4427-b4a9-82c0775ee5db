#!/usr/bin/env python3
"""
COMPREHENSIVE PREVIOUS DATA CREATION
====================================

Creates comprehensive previous period data based on current data
to ensure the comparison phase works correctly.
"""

import sqlite3
import os
import random
from datetime import datetime

def create_comprehensive_previous_data():
    """Create comprehensive previous data for comparison"""
    print("🔧 CREATING COMPREHENSIVE PREVIOUS DATA")
    print("=" * 50)
    
    # Connect to database
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get the latest session
        cursor.execute("""
            SELECT session_id FROM audit_sessions 
            ORDER BY created_at DESC LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No session found")
            return False
        
        session_id = session_result[0]
        print(f"📊 Working with session: {session_id}")
        
        # Clear existing previous data
        cursor.execute("""
            DELETE FROM extracted_data 
            WHERE session_id = ? AND period_type = 'previous'
        """, (session_id,))
        cursor.connection.commit()
        print("🗑️ Cleared existing previous data")
        
        # Get all current data
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value, department
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
            ORDER BY employee_id, section_name, item_label
        """, (session_id,))
        current_data = cursor.fetchall()
        
        print(f"📋 Found {len(current_data)} current records")
        
        if not current_data:
            print("❌ No current data found")
            return False
        
        # Group by employee
        employees = {}
        for emp_id, emp_name, section, item, value, numeric_val, dept in current_data:
            if emp_id not in employees:
                employees[emp_id] = {
                    'name': emp_name,
                    'department': dept,
                    'items': []
                }
            employees[emp_id]['items'].append({
                'section': section,
                'item': item,
                'value': value,
                'numeric': numeric_val
            })
        
        print(f"👥 Processing {len(employees)} unique employees")
        
        # Create previous data for each employee
        total_created = 0
        for emp_id, emp_data in employees.items():
            # Create variations for previous period
            for item_data in emp_data['items']:
                prev_value = item_data['value']
                prev_numeric = item_data['numeric']
                
                # Apply realistic variations for previous period
                if prev_numeric and prev_numeric > 0:
                    # Apply random variation (-10% to +5%)
                    variation = random.uniform(0.90, 1.05)
                    prev_numeric = prev_numeric * variation
                    
                    # Round appropriately
                    if prev_numeric > 100:
                        prev_numeric = round(prev_numeric, 0)
                    else:
                        prev_numeric = round(prev_numeric, 2)
                    
                    prev_value = str(int(prev_numeric)) if prev_numeric == int(prev_numeric) else f"{prev_numeric:.2f}"
                
                # Insert previous data
                cursor.execute("""
                    INSERT INTO extracted_data 
                    (session_id, period_type, employee_id, employee_name, section_name, 
                     item_label, item_value, numeric_value, department, created_at)
                    VALUES (?, 'previous', ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session_id, emp_id, emp_data['name'], item_data['section'],
                    item_data['item'], prev_value, prev_numeric, emp_data['department'],
                    datetime.now()
                ))
                
                total_created += 1
        
        cursor.connection.commit()
        print(f"✅ Created {total_created} previous records")
        
        # Verify the data
        cursor.execute("""
            SELECT COUNT(*), COUNT(DISTINCT employee_id) 
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'previous'
        """, (session_id,))
        total_records, unique_employees = cursor.fetchone()
        
        print(f"✅ Verified: {total_records} records, {unique_employees} unique employees")
        
        # Now run the comparison
        return run_comparison_phase(cursor, session_id)
        
    except Exception as e:
        print(f"❌ Error creating comprehensive data: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

def run_comparison_phase(cursor, session_id):
    """Run the comparison phase"""
    print(f"\n🔄 RUNNING COMPARISON PHASE")
    print("-" * 30)
    
    try:
        # Import the phased process manager
        import sys
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        # Load extracted data
        print("📊 Loading extracted data...")
        current_data = manager._load_extracted_data('current')
        previous_data = manager._load_extracted_data('previous')
        
        print(f"   Current: {len(current_data)} employees")
        print(f"   Previous: {len(previous_data)} employees")
        
        if not current_data or not previous_data:
            print("❌ Missing data for comparison")
            return False
        
        # Clear existing comparison results
        cursor.execute("""
            DELETE FROM comparison_results WHERE session_id = ?
        """, (session_id,))
        cursor.connection.commit()
        
        # Run comparison
        print("🔄 Running comparison...")
        comparison_results = manager._compare_payroll_data(current_data, previous_data)
        
        print(f"📊 Generated {len(comparison_results)} comparison results")
        
        if comparison_results:
            # Store results
            print("💾 Storing comparison results...")
            manager._store_comparison_results(comparison_results)
            
            # Verify storage
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
            stored_count = cursor.fetchone()[0]
            
            print(f"✅ Stored {stored_count} comparison results")
            
            # Show sample results
            cursor.execute("""
                SELECT change_type, COUNT(*) 
                FROM comparison_results 
                WHERE session_id = ? 
                GROUP BY change_type
            """, (session_id,))
            change_summary = cursor.fetchall()
            
            print(f"📊 Change Summary:")
            for change_type, count in change_summary:
                print(f"   {change_type}: {count}")
            
            return True
        else:
            print("❌ No comparison results generated")
            return False
            
    except Exception as e:
        print(f"❌ Error running comparison: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE PREVIOUS DATA CREATION")
    print("=" * 60)
    
    success = create_comprehensive_previous_data()
    
    if success:
        print("\n🎉 COMPREHENSIVE PREVIOUS DATA CREATED SUCCESSFULLY!")
        print("   The comparison phase should now work correctly.")
        print("   Try running the audit process again.")
    else:
        print("\n❌ FAILED TO CREATE COMPREHENSIVE PREVIOUS DATA")
        print("   Manual intervention may be required.")
