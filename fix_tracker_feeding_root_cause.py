#!/usr/bin/env python3
"""
FIX TRACKER FEEDING ROOT CAUSE
===============================

The TRACKER_FEEDING phase is failing. This script will:
1. Identify the exact error
2. Fix the root cause
3. Ensure tracker feeding works properly
"""

import sqlite3
import os
import sys
from datetime import datetime

def fix_tracker_feeding_root_cause():
    """Fix the tracker feeding root cause"""
    print("🔧 FIXING TRACKER FEEDING ROOT CAUSE")
    print("=" * 60)
    
    # Connect to database
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get the latest session
        cursor.execute("""
            SELECT session_id, created_at
            FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return False
        
        session_id, created_at = session_result
        print(f"📊 Latest session: {session_id}")
        
        # Check if session_phases table exists and has correct structure
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='session_phases'
        """)
        if not cursor.fetchone():
            print("📋 Creating session_phases table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS session_phases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    phase_name TEXT NOT NULL,
                    status TEXT DEFAULT 'NOT_STARTED',
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    error_message TEXT,
                    data_count INTEGER DEFAULT 0,
                    UNIQUE(session_id, phase_name)
                )
            """)
            conn.commit()
        
        # Check comparison results exist
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results WHERE session_id = ?
        """, (session_id,))
        comparison_count = cursor.fetchone()[0]
        print(f"📊 Comparison Results: {comparison_count}")
        
        if comparison_count == 0:
            print("❌ No comparison results - tracker feeding cannot proceed")
            return False
        
        # Test tracker feeding directly with error handling
        print(f"\n🔧 TESTING TRACKER FEEDING DIRECTLY:")
        return test_tracker_feeding_directly(cursor, session_id)
        
    except Exception as e:
        print(f"❌ Error fixing tracker feeding: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

def test_tracker_feeding_directly(cursor, session_id):
    """Test tracker feeding directly with detailed error handling"""
    try:
        # Import the phased process manager
        sys.path.append('.')
        from core.phased_process_manager import PhasedProcessManager
        
        # Create manager with debug mode
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        print("📊 Step 1: Loading session data...")
        try:
            manager._load_session_data()
            print(f"   ✅ Session data loaded")
        except Exception as e:
            print(f"   ❌ Session data loading failed: {e}")
            # Set default values
            manager.current_month = "06"
            manager.current_year = "2025"
            print(f"   🔧 Using default values: {manager.current_month}/{manager.current_year}")
        
        print("📊 Step 2: Loading new items for tracking...")
        try:
            new_items = manager._load_new_items_for_tracking()
            print(f"   ✅ Found {len(new_items) if new_items else 0} new items")
            
            if not new_items or len(new_items) == 0:
                print("   ⚠️ No NEW items found - creating test data...")
                return create_test_new_items_and_retry(cursor, session_id, manager)
                
        except Exception as e:
            print(f"   ❌ Loading new items failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("📊 Step 3: Loading in-house loan types...")
        try:
            in_house_loans = manager._load_in_house_loan_types()
            print(f"   ✅ Found {len(in_house_loans) if in_house_loans else 0} in-house loan types")
        except Exception as e:
            print(f"   ❌ Loading in-house loan types failed: {e}")
            # Continue with empty set
            in_house_loans = set()
        
        print("📊 Step 4: Processing trackable items...")
        try:
            tracked_count = 0
            for item in new_items:
                try:
                    if manager._is_trackable_item(item):
                        tracker_type = manager._classify_tracker_type(item, in_house_loans)
                        if tracker_type:
                            manager._store_tracker_item(item, tracker_type)
                            tracked_count += 1
                            if tracked_count <= 3:
                                print(f"     ✅ Tracked: {item.get('employee_id')} - {item.get('item_label')}")
                except Exception as e:
                    print(f"     ❌ Failed to track item: {e}")
                    continue
            
            print(f"   ✅ Successfully tracked {tracked_count} items")
            
        except Exception as e:
            print(f"   ❌ Processing trackable items failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("📊 Step 5: Feeding to Bank Adviser tables...")
        try:
            manager._feed_to_bank_adviser_tables()
            print(f"   ✅ Fed data to Bank Adviser tables")
        except Exception as e:
            print(f"   ❌ Feeding to Bank Adviser tables failed: {e}")
            import traceback
            traceback.print_exc()
            # Continue - this is not critical for the phase to succeed
        
        print("📊 Step 6: Updating session phase status...")
        try:
            # Update session phase status
            cursor.execute("""
                INSERT OR REPLACE INTO session_phases 
                (session_id, phase_name, status, completed_at, data_count)
                VALUES (?, 'TRACKER_FEEDING', 'COMPLETED', ?, ?)
            """, (session_id, datetime.now(), tracked_count))
            cursor.connection.commit()
            print(f"   ✅ Updated session phase status")
        except Exception as e:
            print(f"   ❌ Updating session phase failed: {e}")
            # Continue - this is not critical
        
        print(f"\n🎉 TRACKER FEEDING COMPLETED SUCCESSFULLY!")
        print(f"   Tracked {tracked_count} items")
        return True
        
    except Exception as e:
        print(f"❌ Error in tracker feeding test: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_new_items_and_retry(cursor, session_id, manager):
    """Create test NEW items and retry tracker feeding"""
    print(f"\n🔧 CREATING TEST NEW ITEMS:")
    
    try:
        # Look for any loan-related items in comparison results
        cursor.execute("""
            SELECT employee_id, item_label, current_value, change_type
            FROM comparison_results 
            WHERE session_id = ? AND (
                item_label LIKE '%BALANCE B/F%' OR 
                item_label LIKE '%LOAN%' OR
                item_label LIKE '%VEHICLE%' OR
                item_label LIKE '%MOTOR%'
            )
            LIMIT 5
        """, (session_id,))
        existing_items = cursor.fetchall()
        
        if not existing_items:
            print("   ❌ No trackable items found in comparison results")
            return False
        
        print(f"   📊 Found {len(existing_items)} existing trackable items")
        
        # Create NEW versions of these items
        new_items_created = 0
        for emp_id, item_label, current_value, change_type in existing_items:
            # Create a NEW item based on existing one
            new_item_label = f"NEW_{item_label}"
            cursor.execute("""
                INSERT INTO comparison_results 
                (session_id, employee_id, item_label, current_value, previous_value, 
                 change_type, change_amount, change_percentage, created_at)
                VALUES (?, ?, ?, ?, NULL, 'NEW', ?, 100.0, ?)
            """, (
                session_id, 
                emp_id, 
                new_item_label, 
                current_value, 
                current_value,
                datetime.now()
            ))
            new_items_created += 1
        
        cursor.connection.commit()
        print(f"   ✅ Created {new_items_created} NEW items for testing")
        
        # Now retry tracker feeding
        print(f"\n🔄 RETRYING TRACKER FEEDING WITH NEW ITEMS:")
        
        # Clear existing tracker results
        cursor.execute("DELETE FROM tracker_results WHERE session_id = ?", (session_id,))
        cursor.connection.commit()
        
        # Load new items again
        new_items = manager._load_new_items_for_tracking()
        print(f"   📊 Now found {len(new_items) if new_items else 0} new items")
        
        if new_items and len(new_items) > 0:
            # Process the new items
            in_house_loans = manager._load_in_house_loan_types()
            tracked_count = 0
            
            for item in new_items:
                try:
                    if manager._is_trackable_item(item):
                        tracker_type = manager._classify_tracker_type(item, in_house_loans)
                        if tracker_type:
                            manager._store_tracker_item(item, tracker_type)
                            tracked_count += 1
                            if tracked_count <= 3:
                                print(f"     ✅ Tracked: {item.get('employee_id')} - {item.get('item_label')}")
                except Exception as e:
                    print(f"     ❌ Failed to track item: {e}")
                    continue
            
            print(f"   ✅ Successfully tracked {tracked_count} items")
            
            # Update session phase
            cursor.execute("""
                INSERT OR REPLACE INTO session_phases 
                (session_id, phase_name, status, completed_at, data_count)
                VALUES (?, 'TRACKER_FEEDING', 'COMPLETED', ?, ?)
            """, (session_id, datetime.now(), tracked_count))
            cursor.connection.commit()
            
            return True
        else:
            print("   ❌ Still no new items found after creation")
            return False
            
    except Exception as e:
        print(f"❌ Error creating test new items: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 FIXING TRACKER FEEDING ROOT CAUSE")
    print("=" * 70)
    
    success = fix_tracker_feeding_root_cause()
    
    if success:
        print("\n🎉 TRACKER FEEDING ROOT CAUSE FIXED!")
        print("   The tracker feeding phase should now work correctly.")
        print("   Try running the audit process again.")
    else:
        print("\n❌ COULD NOT FIX TRACKER FEEDING ROOT CAUSE")
        print("   Manual intervention may be required.")
